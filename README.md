# Harun Studio Website

Website resmi Harun Studio — Jasa Pembuatan Website, Maintenance, dan <PERSON>si WordPress/Astro/Next.js.

## ✨ Fitur Utama
- Website statis modern dengan [Astro](https://astro.build/)
- Integrasi TailwindCSS untuk styling
- Konten blog dengan MDX
- Komponen modular dan reusable
- SEO, Sitemap, dan <PERSON><PERSON> otomatis
- Layanan: Pembuatan Website, Maintenance, Migrasi WordPress ke Astro, Konversi ke Block Editor

## 🚀 Instalasi & Pengembangan Lokal
1. **Clone repo:**
   ```bash
   git clone https://github.com/randyhs123/harunstudio.com.git
   cd harunstudio.com
   ```
2. **Install dependencies:**
   ```bash
   npm install
   # atau
   pnpm install
   ```
3. **Jalankan development server:**
   ```bash
   npm run dev
   ```
   Website akan berjalan di `http://localhost:4321` (default Astro).

## 📦 Build untuk Produksi
```bash
npm run build
```
Output akan ada di folder `dist/`.

## 🤝 Kontribusi
- Pull request dan issue sangat diterima!
- <PERSON><PERSON><PERSON> struktur komponen dan konvensi penamaan yang ada.

## 📄 Lisensi
MIT

---

Website ini dikembangkan oleh [Harun Studio](https://harunstudio.com) — Web Developer & WordPress Specialist. 