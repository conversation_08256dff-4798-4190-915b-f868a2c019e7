@import 'tailwindcss';
@plugin '@tailwindcss/typography';
@theme {
  --font-sans:
    Plus Jakarta Sans Variable, ui-sans-serif,
    system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
}
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
  
  :root {
    --heading-font: 'Plus Jakarta Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --heading-letter-spacing: -0.02em;
    --heading-line-height: 100%;
  }
  
  body {
    font-family: var(--heading-font);
    letter-spacing: -0.01em;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6,
  .fs-h1, .fs-h2, .fs-h3, .fs-h4, .fs-h5, .fs-h6 {
    font-family: var(--heading-font);
    letter-spacing: var(--heading-letter-spacing);
    line-height: var(--heading-line-height);
  }
  
  h1, .fs-h1 {
    font-size: clamp(3.25rem, 2.7273rem + 1.2vw, 4rem);
    font-weight: 800;
  }
  
  h2, .fs-h2 {
    font-size: clamp(2.25rem, 2.0455rem + 0.8182vw, 2.7rem);
    font-weight: 700;
  }
  
  h3, .fs-h3 {
    font-size: clamp(1.875rem, 1.7045rem + 0.6818vw, 2.25rem);
    font-weight: 700;
  }
  
  h4, .fs-h4 {
    font-size: clamp(1.5rem, 1.3636rem + 0.5455vw, 1.8rem);
    font-weight: 600;
  }
  
  h5, .fs-h5 {
    font-size: clamp(1.25rem, 1.1364rem + 0.4545vw, 1.5rem);
    font-weight: 600;
  }
  
  h6, .fs-h6 {
    font-size: clamp(1.125rem, 1.0227rem + 0.4091vw, 1.35rem);
    font-weight: 600;
  }
}

:root {
  --color-brand: #2652E3;        
  --color-brand-hover: #111;  
  --color-brand-text: #fff;   
  --color-brand-light: #173CB5; /* Biru lebih terang untuk hover primary */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-brand-50: #EDF1FD; /* Tambahan warna biru sangat ringan */
  --color-brand-100: #D0DAFB; /* Tambahan warna biru ringan */

  /* Neutral/Gray Palette */
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;
}

@layer utilities {
  .hero-main-services {
    @apply pt-32 pb-24 md:pt-40 md:pb-32;
  }
  @media (min-width: 1024px) {
    :global(.hero-main-services) {
      padding-top: 10rem;
    }
  }
  .hero-padding {
    @apply pt-32 pb-20 md:pt-40 md:pb-10;
  }

  .hero-pages {
    @apply pt-32 pb-0 md:pt-40 md:pb-10;
  }
}

/* Blog Image Styles */
.prose img {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  border-radius: 8px;
  margin: 2rem 0;
  max-width: 100%;
  height: auto;
  transition: transform 0.2s ease-in-out;
}

/* Add hover effect for better interactivity */
.prose img:hover {
  transform: translateY(-2px);
}
