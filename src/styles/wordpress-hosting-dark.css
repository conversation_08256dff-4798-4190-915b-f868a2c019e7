/* WordPress Hosting Dark Theme Override */
.wordpress-hosting-dark {
  /* CSS Custom Properties for Dark Theme */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: rgba(30, 41, 59, 0.6);
  --bg-gradient-1: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --bg-gradient-2: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
  --bg-gradient-3: linear-gradient(
    135deg,
    rgba(30, 58, 138, 0.1) 0%,
    rgba(124, 58, 237, 0.1) 100%
  );

  --text-primary: #ffffff;
  --text-secondary: rgba(191, 219, 254, 0.9);
  --text-muted: rgba(191, 219, 254, 0.7);

  --brand-primary: #3b82f6;
  --brand-secondary: #8b5cf6;
  --brand-accent: #06b6d4;

  --border-primary: rgba(59, 130, 246, 0.2);
  --border-secondary: rgba(139, 92, 246, 0.2);
  --border-muted: rgba(71, 85, 105, 0.3);

  --shadow-primary: 0 10px 25px rgba(59, 130, 246, 0.15);
  --shadow-secondary: 0 10px 25px rgba(139, 92, 246, 0.15);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.wordpress-hosting-dark {
  background: var(--bg-primary);
  min-height: 100vh;
}

/* Override main backgrounds */
.wordpress-hosting-dark section {
  background: transparent !important;
}

.wordpress-hosting-dark section:nth-child(even) {
  background: var(--bg-gradient-3) !important;
}

/* Text Color Overrides */
.wordpress-hosting-dark h1,
.wordpress-hosting-dark h2,
.wordpress-hosting-dark h3,
.wordpress-hosting-dark h4,
.wordpress-hosting-dark h5,
.wordpress-hosting-dark h6 {
  color: var(--text-primary) !important;
  background: linear-gradient(135deg, #bfdbfe 0%, #c4b5fd 50%, #a5f3fc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.wordpress-hosting-dark p,
.wordpress-hosting-dark span,
.wordpress-hosting-dark div {
  color: var(--text-secondary) !important;
}

.wordpress-hosting-dark .text-gray-600,
.wordpress-hosting-dark .text-gray-700,
.wordpress-hosting-dark .text-gray-800,
.wordpress-hosting-dark .text-gray-900 {
  color: var(--text-secondary) !important;
}

/* Card Overrides */
.wordpress-hosting-dark .bg-white {
  background: var(--bg-card) !important;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-muted) !important;
  box-shadow: var(--shadow-card) !important;
}

.wordpress-hosting-dark .border-gray-100,
.wordpress-hosting-dark .border-gray-200 {
  border-color: var(--border-muted) !important;
}

/* Brand Color Applications */
.wordpress-hosting-dark [style*="var(--color-brand)"] {
  color: var(--brand-primary) !important;
}

.wordpress-hosting-dark .text-\[var\(--color-brand\)\] {
  color: var(--brand-primary) !important;
}

.wordpress-hosting-dark .bg-\[var\(--color-brand-50\)\] {
  background: rgba(59, 130, 246, 0.1) !important;
}

/* Button Overrides */
.wordpress-hosting-dark button,
.wordpress-hosting-dark .btn,
.wordpress-hosting-dark a[class*="button"],
.wordpress-hosting-dark .styled-button {
  background: var(--bg-gradient-2) !important;
  border: none !important;
  color: white !important;
  box-shadow: var(--shadow-primary) !important;
  transition: all 0.3s ease !important;
}

.wordpress-hosting-dark button:hover,
.wordpress-hosting-dark .btn:hover,
.wordpress-hosting-dark a[class*="button"]:hover,
.wordpress-hosting-dark .styled-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 15px 30px rgba(59, 130, 246, 0.25) !important;
}

/* Dark Theme Specific Neutral Button Style */
.wordpress-hosting-dark .btn-style-neutral {
  background: transparent !important; /* Remove gradient background */
  border: 1px solid var(--border-primary) !important; /* Add a subtle border */
  color: var(--text-primary) !important; /* Use primary text color */
  box-shadow: none !important; /* Remove shadow */
}

.wordpress-hosting-dark .btn-style-neutral:hover {
  background: rgba(59, 130, 246, 0.1) !important; /* Subtle hover background */
  transform: none !important; /* Remove hover translateY effect */
  box-shadow: none !important; /* Remove hover shadow */
}

/* Pricing Cards Special Styling */
.wordpress-hosting-dark .grid > div:has(.text-2xl) {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  position: relative;
  overflow: hidden;
}

.wordpress-hosting-dark .grid > div:has(.text-2xl):hover {
  border-color: var(--brand-primary) !important;
  box-shadow: var(--shadow-primary) !important;
  transform: translateY(-4px) !important;
}

.wordpress-hosting-dark .grid > div:has(.text-2xl)::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bg-gradient-2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wordpress-hosting-dark .grid > div:has(.text-2xl):hover::before {
  opacity: 1;
}

/* Icon Styling */
.wordpress-hosting-dark svg {
  color: var(--brand-primary) !important;
}

.wordpress-hosting-dark .w-16.h-16 {
  background: var(--bg-gradient-2) !important;
  box-shadow: var(--shadow-primary) !important;
}

/* Check Icons */
.wordpress-hosting-dark .w-5.h-5:has(svg[name*="check"]) {
  background: rgba(59, 130, 246, 0.2) !important;
  border: 1px solid var(--border-primary) !important;
}

/* Testimonial Section */
.wordpress-hosting-dark .grid:has(.text-3xl) {
  border-radius: 16px !important;
  padding: 32px !important;
  backdrop-filter: blur(10px);
}

/* FAQ Section */
.wordpress-hosting-dark details {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-muted) !important;
  backdrop-filter: blur(10px);
}

.wordpress-hosting-dark details:hover {
  border-color: var(--border-primary) !important;
  box-shadow: var(--shadow-card) !important;
}

.wordpress-hosting-dark details[open] {
  border-color: var(--brand-primary) !important;
}

/* Plus Features Section */
.wordpress-hosting-dark .bg-white\/90 {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-secondary) !important;
}

.wordpress-hosting-dark .border-2.border-gray-200 {
  border-color: var(--border-secondary) !important;
}

/* Hero Section Enhancements */
.wordpress-hosting-dark section:first-child {
  background: var(--bg-gradient-1) !important;
  position: relative;
  overflow: hidden;
}

.wordpress-hosting-dark section:first-child::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(
      circle at 30% 20%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(139, 92, 246, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: 1;
}

.wordpress-hosting-dark section:first-child > * {
  position: relative;
  z-index: 2;
}

/* Image Enhancements */
.wordpress-hosting-dark img {
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
}

/* Smooth Transitions */
.wordpress-hosting-dark * {
  transition: all 0.3s ease !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .wordpress-hosting-dark .grid > div:nth-child(2):has(.text-2xl) {
    transform: none;
  }
}

/* Animation Enhancements */
@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

.wordpress-hosting-dark .grid > div:nth-child(2):has(.text-2xl) {
  animation: glow 3s ease-in-out infinite;
}

.wordpress-hosting-dark section:nth-child(2n) {
  background: none !important;
}

/* Urgency Badge Dark Theme */
.wordpress-hosting-dark .urgency-badge {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%) !important;
  border: 1px solid rgba(245, 158, 11, 0.3) !important;
}

.wordpress-hosting-dark .urgency-badge span {
  background: linear-gradient(135deg, #fde68a 0%, #fbbf24 50%, #f97316 100%); /* Yellow to Orange gradient */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.wordpress-hosting-dark .urgency-badge svg {
  color: #f97316 !important; /* Match icon color to the orange in gradient */
}

/* FAQ Section Dark Theme Overrides */
.wordpress-hosting-dark .max-w-3xl.mx-auto > .space-y-3 > div, /* Targeting the wrapping div */
.wordpress-hosting-dark .max-w-3xl.mx-auto > .space-y-4 > div { /* Targeting the wrapping div */
  border-color: var(--border-muted) !important;
  background: var(--bg-card) !important; /* Override bg-white */
}

.wordpress-hosting-dark details.group summary {
  background: transparent !important; /* Ensure summary background is transparent or matches card */
  color: var(--text-primary) !important; /* Question text color */
}

.wordpress-hosting-dark details.group summary:hover {
  background: rgba(51, 65, 85, 0.5) !important; /* Subtle hover background */
}

.wordpress-hosting-dark details.group summary h3 {
   color: var(--text-primary) !important; /* Ensure question text is primary color */
}

.wordpress-hosting-dark details.group .border-t.border-gray-100.bg-gray-50 { /* Targeting content div */
  border-color: var(--border-muted) !important;
  background: transparent !important; /* Override bg-gray-50 */
}

.wordpress-hosting-dark details.group .border-t.border-gray-100.bg-gray-50 p {
  color: var(--text-secondary) !important; /* Ensure answer text is secondary color */
}

/* Process Section Dark Theme Overrides */
.wordpress-hosting-dark .process-step {
  border-color: var(--border-muted) !important;
  background: var(--bg-card) !important; /* Override bg-white */
}

.wordpress-hosting-dark .process-header {
  background: transparent !important; /* Ensure header background is transparent or matches card */
}

.wordpress-hosting-dark .process-header:hover {
  background: rgba(51, 65, 85, 0.5) !important; /* Subtle hover background */
}

.wordpress-hosting-dark .process-step h3 {
  color: var(--text-primary) !important; /* Process step title color */
}

.wordpress-hosting-dark .process-step p {
  color: var(--text-secondary) !important; /* Process step description color */
}

.wordpress-hosting-dark .process-content .border-t.border-gray-100 {
  border-color: var(--border-muted) !important;
}

.wordpress-hosting-dark .process-content .bg-gray-50 {
  background: transparent !important; /* Override inner bg-gray-50 */
}

.wordpress-hosting-dark .process-content .bg-gray-50 p {
   color: var(--text-secondary) !important; /* Ensure text in inner div is secondary color */
}

.wordpress-hosting-dark .process-step .text-gray-500 {
  color: var(--text-secondary) !important; /* Override specific gray text color */
}

.wordpress-hosting-dark .process-step .text-gray-600 {
  color: var(--text-secondary) !important; /* Override specific gray text color */
}

.wordpress-hosting-dark .process-step .text-gray-700 {
  color: var(--text-secondary) !important; /* Override specific gray text color */
}

.wordpress-hosting-dark .process-step .text-gray-900 {
  color: var(--text-primary) !important; /* Override specific gray text color */
}

.wordpress-hosting-dark .process-step .accordion-arrow svg {
  color: var(--text-muted) !important; /* Darken arrow color */
}

/* CTA Link Button Dark Theme Override */
.wordpress-hosting-dark a.border.border-white\/20 {
    background: var(--bg-gradient-2) !important;
    border: none !important;
    color: white !important;
    box-shadow: var(--shadow-primary) !important;
    transition: all 0.3s ease !important;
}

.wordpress-hosting-dark a.border.border-white\/20:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.25) !important;
}

/* Plus Features Section Dark Theme Overrides */
.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) {
  border-color: rgba(245, 158, 11, 0.4) !important; /* Change border color to orange */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(245, 158, 11, 0.4) !important; /* Add orange glow to shadow */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .absolute.top-0.left-0.w-full.h-1 {
    background: linear-gradient(135deg, #f97316 0%, #fbbf24 100%) !important; /* Use orange gradient */
    opacity: 0.8 !important; /* Increase opacity */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .inline-flex.items-center.px-3.py-1.rounded-full {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(234, 88, 12, 0.2) 100%) !important; /* Orange gradient background */
    border-color: rgba(245, 158, 11, 0.4) !important; /* Orange border */
    color: #f97316 !important; /* Orange text color */
    box-shadow: none !important; /* Remove default shadow */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .inline-flex.items-center.px-3.py-1.rounded-full span {
    color: inherit !important; /* Inherit color from parent */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .inline-flex.items-center.px-3.py-1.rounded-full svg {
    color: #f97316 !important; /* Orange icon color */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .w-12.h-12.rounded-full {
    background: rgba(245, 158, 11, 0.1) !important; /* Orange light background */
    border-color: rgba(245, 158, 11, 0.4) !important; /* Orange border */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .w-12.h-12.rounded-full svg {
    color: #f97316 !important; /* Orange icon color */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) h3 {
    color: var(--text-primary) !important; /* Ensure title is primary text color */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .w-5.h-5.rounded-full {
     background: rgba(245, 158, 11, 0.2) !important; /* Orange light background */
     border-color: rgba(245, 158, 11, 0.4) !important; /* Orange border */
}

.wordpress-hosting-dark .grid > div:has(.inline-flex.items-center.px-3.py-1.rounded-full) .w-5.h-5.rounded-full svg {
    color: #f97316 !important; /* Orange icon color */
}
