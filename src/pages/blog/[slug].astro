---
import { getCollection } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";
import { Image } from "astro:assets";
import authorImage from "../../assets/images/Randika.jpg";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

// Generate a new path for every collection entry
export async function getStaticPaths() {
  const blogEntries = await getCollection("blog");
  return blogEntries.map((entry) => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

// Get the entry directly from the prop on render
const { entry } = Astro.props;
const { Content } = await entry.render();

// Membuat description fallback jika skema belum direfresh
// @ts-ignore - Abaikan error TypeScript karena description sudah ditambahkan di skema
const description = entry.data.description || 
  entry.data.snippet || // Gunakan snippet jika ada
  `Artikel tentang ${entry.data.title} oleh ${entry.data.author}`;
---

<Layout title={entry.data.title} description={description}>
    <!-- Background design elements -->
    <BackgroundElements />
  <Container>
    <div class="mx-auto max-w-3xl pt-24 md:pt-28">
      <div class="mb-2">
        <span class="px-3 py-1 bg-[var(--color-brand-50)] rounded-full text-xs font-medium text-[var(--color-brand)]">
          {entry.data.category}
        </span>
      </div>
      
      <h1 class="text-4xl lg:text-5xl font-bold lg:tracking-tight mt-1 lg:leading-tight mb-4">
        {entry.data.title}
      </h1>
      
      <div class="flex items-center border-b border-gray-100 pb-4">
        <span class="font-medium text-gray-900">{entry.data.author}</span>
        <span class="mx-2 text-gray-400">|</span>
        <time class="text-gray-500 text-sm" datetime={entry.data.publishDate.toISOString()}>
          {new Date(entry.data.publishDate).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </time>
      </div>

      <!-- Featured Image -->
      <div class="rounded-xl overflow-hidden mb-5">
        <Image
          src={entry.data.image.src}
          alt={entry.data.image.alt}
          width={1200}
          height={630}
          class="w-full object-cover"
        />
      </div>
    </div>
  </Container>

  <Container>
    <div class="mx-auto max-w-3xl">
      <div class="prose prose-headings:font-bold prose-headings:text-gray-900 prose-p:text-gray-700 prose-img:rounded-xl prose-a:text-[var(--color-brand)] max-w-none">
        <Content />
      </div>
      
      <!-- Author Box -->
      <div class="bg-gray-50 rounded-xl p-8 mb-12 mt-6 flex flex-col md:flex-row gap-6 items-start md:items-center">
        <div class="w-20 h-20 rounded-full overflow-hidden flex-shrink-0">
          <Image 
            src={authorImage} 
            alt="Willya Randika" 
            width={80} 
            height={80}
            class="w-full h-full object-cover" 
          />
        </div>
        <div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">Willya Randika</h3>
          <p class="text-gray-700">
            Founder Harun Studio & web developer, blogger, serta hosting reviewer. Telah membantu pemilik bisnis meraih kesuksesan dengan design, development dan maintenance sejak 2021.
          </p>
        </div>
      </div>
      
      <div class="border-t border-gray-100 pt-6 flex flex-wrap gap-3 mb-16">
        {entry.data.tags.map((tag) => (
          <span class="px-3 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-700 hover:bg-[var(--color-brand-50)] hover:text-[var(--color-brand)] transition-colors">
            #{tag}
          </span>
        ))}
      </div>
      
      <div class="flex justify-center mb-16">
        <a href="/blog" class="inline-flex items-center px-5 py-3 bg-[var(--color-brand-50)] text-[var(--color-brand)] font-medium rounded-lg hover:bg-[var(--color-brand-100)] transition-colors">
          <Icon name="lucide:arrow-left" class="w-4 h-4 mr-2" />
          Kembali ke Blog
        </a>
      </div>
    </div>
  </Container>
</Layout>
