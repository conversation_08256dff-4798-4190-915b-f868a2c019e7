---
import Container from "@/components/container.astro";
import Layout from "@/layouts/Layout.astro";
import { Icon } from "astro-icon/components";
import SecurityPlugins from "@/components/plugin-wordpress/SecurityPlugins.astro";
import PerformancePlugins from "@/components/plugin-wordpress/PerformancePlugins.astro";
import SeoPlugins from "@/components/plugin-wordpress/SeoPlugins.astro";
import ContentPlugins from "@/components/plugin-wordpress/ContentPlugins.astro";
import FormPlugins from "@/components/plugin-wordpress/FormPlugins.astro";
import DevelopmentPlugins from "@/components/plugin-wordpress/DevelopmentPlugins.astro";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

const metaDescription = "Rekomendasi plugin WordPress terbaik untuk keamanan, performa (kecepatan), SEO, konten, formulir, dan pengembangan. Pilihan aman & ringan.";

// Definisi untuk kategori tab
const categories = [
  { id: "security", label: "Plugin Keamanan", icon: "lucide:shield" },
  { id: "performance", label: "Plugin Optimasi Kece<PERSON>", icon: "lucide:zap" },
  { id: "seo", label: "Plugin SEO", icon: "lucide:search" },
  { id: "content", label: "Plugin Manajemen Konten", icon: "lucide:file-text" },
  { id: "form", label: "Plugin Formulir dan SMTP", icon: "lucide:mail" },
  { id: "development", label: "Plugin Pengembangan", icon: "lucide:code" }
];
---

<Layout title="Plugin WordPress Terbaik" description={metaDescription}>
    <!-- Background design elements -->
  <BackgroundElements />
  <div class="relative hero-padding">
    <Container>
      <div class="max-w-4xl mx-auto px-4 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Plugin WordPress Terbaik</h1>
        <p class="text-lg text-gray-600 max-w-3xl mx-auto">
          Sedang mencari plugin untuk ditambahkan ke WordPress Anda? Jelajahi rekomendasi plugin kami. Di Harun Studio, kami hanya merekomendasikan plugin yang aman, ringan dan dapat membantu kesuksesan website Anda.
        </p>
        
        <!-- Categories Navigation - Tabs -->
        <div id="plugin-categories-tabs" class="mt-12 flex flex-wrap justify-center gap-2 md:gap-4">
          {categories.map((category, index) => (
            <button 
              data-category={category.id}
              class={`flex items-center px-4 py-3 rounded-lg bg-white hover:bg-[var(--color-brand)] hover:text-white text-gray-700 transition-colors border border-gray-200 shadow-sm ${index === 0 ? 'bg-[var(--color-brand)] text-white' : ''}`}
            >
              <Icon name={category.icon} class="w-5 h-5 mr-2" />
              <span class="font-medium">{category.label}</span>
            </button>
          ))}
        </div>
      </div>
    </Container>
  </div>

  <!-- Plugin Sections -->
  <div class="py-16">
    <Container>
      <div id="security" class="plugin-category-content">
      <SecurityPlugins />
      </div>
      <div id="performance" class="plugin-category-content hidden">
      <PerformancePlugins />
      </div>
      <div id="seo" class="plugin-category-content hidden">
      <SeoPlugins />
      </div>
      <div id="content" class="plugin-category-content hidden">
      <ContentPlugins />
      </div>
      <div id="form" class="plugin-category-content hidden">
      <FormPlugins />
      </div>
      <div id="development" class="plugin-category-content hidden">
      <DevelopmentPlugins />
      </div>
      
      <!-- CTA Section -->
      <section class="bg-gradient-to-r from-gray-50 to-white rounded-xl p-8 md:p-12 shadow-sm border border-gray-100">
        <div class="max-w-4xl mx-auto text-center">
          <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">Butuh Bantuan dengan Website WordPress Anda?</h2>
          <p class="text-lg text-gray-600 mb-8 max-w-3xl mx-auto">
            Apakah Anda memerlukan bantuan untuk mengoptimalkan atau memperbaiki website WordPress Anda? Tim kami siap membantu Anda dengan solusi yang tepat sesuai kebutuhan Anda.
          </p>
          
          <a href="/hubungi-kami" class="inline-flex items-center justify-center px-6 py-3 bg-[var(--color-brand)] hover:bg-[var(--color-brand-hover)] text-white font-medium rounded-md transition-colors text-lg">
            Konsultasikan Website Anda
            <Icon name="lucide:arrow-right" class="ml-2 w-5 h-5" />
          </a>
        </div>
      </section>
    </Container>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const tabs = document.querySelectorAll('#plugin-categories-tabs button');
      const contents = document.querySelectorAll('.plugin-category-content');

      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          const buttonTab = tab as HTMLButtonElement;
          const categoryId = buttonTab.dataset.category;

          // Remove active class from all tabs and hide all contents
          tabs.forEach(t => {
              const currentTab = t as HTMLButtonElement;
              currentTab.classList.remove('bg-[var(--color-brand)]', 'text-white');
              currentTab.classList.add('bg-white', 'text-gray-700');
          });
          contents.forEach(content => {
              // Ensure content is an HTMLElement before adding class
              if (content instanceof HTMLElement) {
                  content.classList.add('hidden');
              }
          });

          // Add active class to clicked tab and show corresponding content
          buttonTab.classList.add('bg-[var(--color-brand)]', 'text-white');
          buttonTab.classList.remove('bg-white', 'text-gray-700');

          // Check if categoryId is a valid string and the element exists
          if (typeof categoryId === 'string' && categoryId) {
              const contentElement = document.getElementById(categoryId);
              if (contentElement) {
                  contentElement.classList.remove('hidden');
              }
          }
        });
      });

      // Initial active state for the first tab and content
      if (tabs.length > 0 && contents.length > 0) {
          const firstTab = tabs[0] as HTMLButtonElement;
          firstTab.classList.add('bg-[var(--color-brand)]', 'text-white');
          firstTab.classList.remove('bg-white', 'text-gray-700');

          const firstCategoryId = firstTab.dataset.category;
          // Check if firstCategoryId is a valid string and the element exists
          if (typeof firstCategoryId === 'string' && firstCategoryId) {
              const firstContentElement = document.getElementById(firstCategoryId);
               if (firstContentElement) {
                   firstContentElement.classList.remove('hidden');
               }
          }
      }
    });
  </script>
</Layout>