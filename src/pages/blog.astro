---
import { getCollection } from "astro:content";
import { Picture } from "astro:assets";
import Layout from "@/layouts/Layout.astro";
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

const metaDescription = "Blog Harun Studio: Baca tips, insight, studi kasus, dan panduan terbaru seputar WordPress, Web Development (Astro, Next.js), SEO, dan performa website.";

// Filter blog entries with 'draft: false' & date before current date
const publishedBlogEntries = await getCollection("blog", ({ data }) => {
  return !data.draft && data.publishDate < new Date();
});

// Sort content entries by publication date
publishedBlogEntries.sort(function (a, b) {
  return b.data.publishDate.valueOf() - a.data.publishDate.valueOf();
});
---

<Layout title="Blog" description={metaDescription}>
  <!-- Background design elements -->
  <BackgroundElements />
  <div class="relative pt-32 pb-5 md:pt-40 md:pb-10">    
    <Container>
      <div class="flex flex-col items-center text-center">
        <h1 class="mb-4">
          Blog
        </h1>
        <p class="text-lg max-w-xl text-gray-600">
          Tips, insight, dan panduan terbaru seputar WordPress dan pengembangan website
        </p>
      </div>
    </Container>
  </div>

  <Container>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 mt-10 mb-20">
      {
        publishedBlogEntries.map((blogPostEntry) => (
          <div class="bg-white rounded-xl shadow-sm overflow-hidden flex flex-col h-full hover:shadow-md transition-shadow border border-gray-100">
            <a href={`/blog/${blogPostEntry.slug}`} class="block overflow-hidden">
              <div class="aspect-video overflow-hidden">
                <Picture
                  src={blogPostEntry.data.image.src}
                  alt={blogPostEntry.data.image.alt}
                  sizes="(max-width: 800px) 100vw, 400px"
                  width={400}
                  height={250}
                  class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
              </div>
            </a>
            
            <div class="p-6 flex flex-col flex-1">
              <div class="flex items-center gap-2 mb-3">
                <span class="px-3 py-1 bg-[var(--color-brand-50)] rounded-full text-xs font-medium text-[var(--color-brand)]">
                  {blogPostEntry.data.category}
                </span>
                <span class="text-sm text-gray-500">
                  {new Date(blogPostEntry.data.publishDate).toLocaleDateString('id-ID', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
              
              <a href={`/blog/${blogPostEntry.slug}`} class="group">
                <h2 class="text-xl font-semibold leading-snug tracking-tight mb-3 group-hover:text-[var(--color-brand)] transition-colors">
                  {blogPostEntry.data.title}
                </h2>
              </a>
              
              <p class="text-gray-600 mb-4 flex-1">
                {blogPostEntry.data.snippet}...
              </p>
              
              <div class="flex items-center justify-between mt-auto pt-4 border-t border-gray-100">
                <span class="text-sm text-gray-600">
                  {blogPostEntry.data.author}
                </span>
                
                <a href={`/blog/${blogPostEntry.slug}`} class="text-[var(--color-brand)] font-medium hover:underline inline-flex items-center">
                  Baca Artikel
                  <Icon name="lucide:arrow-right" class="w-4 h-4 ml-1" />
                </a>
              </div>
            </div>
          </div>
        ))
      }
    </div>
  </Container>
</Layout>
