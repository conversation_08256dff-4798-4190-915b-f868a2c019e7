---
import Contactform from "@/components/contactform.astro";
import Container from "@/components/container.astro";
import Layout from "@/layouts/Layout.astro";
import { Icon } from "astro-icon/components";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

const metaDescription = "Hubungi tim <PERSON> via Telepon, WhatsApp, atau Email. Dapatkan konsultasi gratis untuk kebutuhan website Anda. Respons cepat di jam kerja.";
---

<Layout title="Hubungi Kami" description={metaDescription}>
  <!-- Background design elements -->
  <BackgroundElements />
  <div class="relative hero-pages"
    <Container>
      <div class="flex flex-col w-full max-w-[55ch] mx-auto px-4 text-center">
        <h1 class="mt-2 mb-6">Hubungi <PERSON></h1>
        <p class="text-lg md:text-xl text-gray-600 mb-10 leading-relaxed">
          Siap memulai atau punya pertanyaan? <PERSON> kami siap membantu mewujudkan visi digital Anda dengan solusi yang tepat untuk kebutuhan spesifik bisnis Anda.
        </p>
      </div>
    </Container>
  </div>

  <!-- Content Section dengan Design Modern -->
  <div class="pt-10 pb-16">
    <Container>
      <div class="grid lg:grid-cols-3 gap-10 mx-auto max-w-6xl relative z-10">
        <!-- Contact Info Card - 1/3 width pada desktop -->
        <div class="lg:col-span-1 order-2 lg:order-1">
          <div class="bg-white p-8 rounded-xl shadow-sm border border-gray-100 sticky top-8">
            <h2 class="font-bold text-2xl text-gray-800 mb-6">Kontak Kami</h2>
            
            <div class="space-y-6">
              <div>
                <h3 class="text-sm uppercase tracking-wider text-gray-500 font-medium mb-3">Hubungi Langsung</h3>
                <div class="space-y-4">
                  <div class="flex items-start space-x-3 text-gray-600">
                    <div class="mt-1">
                      <Icon class="text-[var(--color-brand)] w-5 h-5" name="lucide:phone" />
                    </div>
                    <div>
                      <p class="font-medium text-gray-900">Telepon/WhatsApp</p>
                      <a href="tel:+6281291274023" class="hover:text-[var(--color-brand)] transition-colors">+62 812-9127-4023</a>
                    </div>
                  </div>
                  
                  <div class="flex items-start space-x-3 text-gray-600">
                    <div class="mt-1">
                      <Icon class="text-[var(--color-brand)] w-5 h-5" name="lucide:mail" />
                    </div>
                    <div>
                      <p class="font-medium text-gray-900">Email</p>
                      <a href="mailto:<EMAIL>" class="hover:text-[var(--color-brand)] transition-colors"><EMAIL></a>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="border-t border-gray-100 pt-6">
                <h3 class="text-sm uppercase tracking-wider text-gray-500 font-medium mb-3">Jam Kerja</h3>
                <p class="text-gray-700">Senin - Jumat: 09:00 - 17:00 WIB</p>
                <p class="text-gray-700">Sabtu - Minggu: Dengan perjanjian</p>
              </div>
              
              <div class="border-t border-gray-100 pt-6">
                <h3 class="text-sm uppercase tracking-wider text-gray-500 font-medium mb-3">Respons Cepat</h3>
                <p class="text-gray-700 mb-4">Kami berkomitmen merespons semua permintaan dalam waktu kurang dari 30 menit di jam kerja atau 1 jam di luar jam kerja.</p>
                <div class="flex space-x-3">
                  <a href="https://wa.me/6281291274023" class="bg-green-500 hover:bg-green-600 text-white p-2 rounded-full transition-colors" aria-label="WhatsApp">
                    <Icon name="lucide:message-circle" class="w-5 h-5" />
                  </a>
                  <a href="mailto:<EMAIL>" class="bg-[var(--color-brand)] hover:bg-[var(--color-brand-hover)] text-white p-2 rounded-full transition-colors" aria-label="Email">
                    <Icon name="lucide:mail" class="w-5 h-5" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Contact Form Card - 2/3 width pada desktop -->
        <div class="lg:col-span-2 order-1 lg:order-2">
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Contactform />
          </div>
        </div>
      </div>
    </Container>
  </div>
</Layout>
