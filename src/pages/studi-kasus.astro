---
import { getCollection } from 'astro:content';
import Layout from "@/layouts/Layout.astro";
import { Image } from "astro:assets";
import Button from "@/components/ui/button.astro";
import { Icon } from "astro-icon/components";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

// Ambil semua blog post dengan kategori "STUDI KASUS"
const allPosts = await getCollection('blog');
const studiKasus = allPosts
  .filter(post => post.data.category === "STUDI KASUS" && !post.data.draft)
  .sort((a, b) => b.data.publishDate.valueOf() - a.data.publishDate.valueOf());

// Format tanggal
function formatDate(date) {
  // Menggunakan pendekatan manual untuk format tanggal karena masalah locale
  const months = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Jun<PERSON>',
    '<PERSON><PERSON>', '<PERSON>gustus', 'September', 'Okto<PERSON>', 'November', 'Desember'
  ];
  const d = new Date(date);
  return `${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`;
}

// Meta data halaman
const title = "Studi Kasus - Proyek Sukses Harun Studio";
const description = "Lihat bukti nyata keberhasilan kami dalam meningkatkan kecepatan, performa, dan UX pada website klien. Studi kasus lengkap dengan data dan hasil.";
const ogImage = "/assets/pages/studi-kasus-harun-studio.webp";
const ogImageAlt = "Studi Kasus Sukses Harun Studio";
---

<Layout 
  title={title}
  description={description}
  ogImage={ogImage}
  ogImageAlt={ogImageAlt}
>
  <div class="mb-20">
    <!-- Background design elements -->
    <BackgroundElements />
    <!-- Hero Section -->
    <section class="relative hero-pages">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="text-center mb-10 md:mb-16">
          <span class="bg-blue-100 text-blue-700 rounded-full px-4 py-1 text-sm font-semibold mb-4 inline-block">BUKTI NYATA</span>
          <h1 class="text-3xl md:text-5xl font-bold mb-6" style="color: var(--color-gray-900)">Studi Kasus Sukses</h1>
          <p class="text-lg md:text-xl max-w-3xl mx-auto" style="color: var(--color-gray-700)">
            Lihat bagaimana kami membantu klien meningkatkan performa website, kecepatan loading, dan pengalaman pengguna dengan solusi yang kami tawarkan.
          </p>
        </div>
      </div>
    </section>

    <!-- Case Studies List -->
    <section>
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="grid gap-12">
          {studiKasus.map((post) => (
            <div class="flex flex-col md:flex-row gap-8 md:gap-12 items-center">
              <!-- Image -->
              <div class="w-full md:w-2/5 lg:w-1/3">
                <a href={`/blog/${post.slug}`} class="block overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <Image
                    src={post.data.image.src}
                    alt={post.data.image.alt || post.data.title}
                    width={600}
                    height={400}
                    class="w-full h-auto object-cover transition-transform duration-500 hover:scale-105"
                  />
                </a>
              </div>
              
              <!-- Content -->
              <div class="w-full md:w-3/5 lg:w-2/3">
                <div class="flex flex-col h-full">
                  <!-- Category & Date -->
                  <div class="flex items-center gap-4 mb-3">
                    <span class="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full">
                      {post.data.category}
                    </span>
                    <span class="text-sm text-gray-500">
                      {formatDate(post.data.publishDate)}
                    </span>
                  </div>
                  
                  <!-- Title -->
                  <h2 class="text-xl md:text-2xl font-bold mb-3" style="color: var(--color-gray-900)">{post.data.title}</h2>
                  
                  <p class="text-base mb-5" style="color: var(--color-gray-700)">
                    {post.data.snippet || ""}
                  </p>
                  
                  <!-- Metrics Section -->
                  <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-5">
                    {post.slug.includes('win-equipment') && (
                      <>
                        <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-emerald-600 font-medium">PageSpeed Mobile</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-emerald-700">27 → 92</span>
                            <span class="text-xs font-semibold text-emerald-600">+240%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-blue-600 font-medium">PageSpeed Desktop</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-blue-700">36 → 99</span>
                            <span class="text-xs font-semibold text-blue-600">+177%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-amber-50 to-amber-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-amber-600 font-medium">Plugin Reduction</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-amber-700">43 → 19</span>
                            <span class="text-xs font-semibold text-amber-600">-56%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-purple-600 font-medium">Load Time</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-purple-700">5.2s → 1.1s</span>
                            <span class="text-xs font-semibold text-purple-600">-79%</span>
                          </div>
                        </div>
                      </>
                    )}
                    
                    {post.slug.includes('harun-studio') && (
                      <>
                        <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-emerald-600 font-medium">PageSpeed Mobile</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-emerald-700">64 → 97</span>
                            <span class="text-xs font-semibold text-emerald-600">+51%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-blue-600 font-medium">PageSpeed Desktop</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-blue-700">82 → 100</span>
                            <span class="text-xs font-semibold text-blue-600">+22%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-amber-50 to-amber-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-amber-600 font-medium">Build Time</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-amber-700">12s → 0.2s</span>
                            <span class="text-xs font-semibold text-amber-600">-98%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-purple-600 font-medium">Hosting Cost</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-purple-700">$5.28 → $0</span>
                            <span class="text-xs font-semibold text-purple-600">-100%</span>
                          </div>
                        </div>
                      </>
                    )}
                    
                    {post.slug.includes('kecepatan-website-wordpress') && (
                      <>
                        <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-emerald-600 font-medium">PageSpeed Mobile</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-emerald-700">43 → 95</span>
                            <span class="text-xs font-semibold text-emerald-600">+121%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-blue-600 font-medium">PageSpeed Desktop</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-blue-700">56 → 99</span>
                            <span class="text-xs font-semibold text-blue-600">+77%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-amber-50 to-amber-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-amber-600 font-medium">LCP</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-amber-700">4.8s → 1.2s</span>
                            <span class="text-xs font-semibold text-amber-600">-75%</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-purple-600 font-medium">CLS</p>
                          <div class="flex items-center justify-center gap-1 mt-1">
                            <span class="text-xl font-bold text-purple-700">0.24 → 0.01</span>
                            <span class="text-xs font-semibold text-purple-600">-96%</span>
                          </div>
                        </div>
                      </>
                    )}
                    
                    {post.slug.includes('muslimadani') && (
                      <>
                        <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-emerald-600 font-medium">UX Improvement</p>
                          <div class="flex items-center justify-center mt-1">
                            <span class="text-xl font-bold text-emerald-700">7+ Masalah Diperbaiki</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-blue-600 font-medium">Teknologi</p>
                          <div class="flex items-center justify-center mt-1">
                            <span class="text-xl font-bold text-blue-700">Elementor → Blocks</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-amber-50 to-amber-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-amber-600 font-medium">Hosting</p>
                          <div class="flex items-center justify-center mt-1">
                            <span class="text-xl font-bold text-amber-700">Shared → VPS</span>
                          </div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg text-center">
                          <p class="text-xs text-purple-600 font-medium">Kode Kustom</p>
                          <div class="flex items-center justify-center mt-1">
                            <span class="text-xl font-bold text-purple-700">1000+ Baris</span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                  
                  <!-- Tags -->
                  <div class="flex flex-wrap gap-2 mb-6">
                    {post.data.tags && post.data.tags.map((tag) => (
                      <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  <!-- Button -->
                  <div class="mt-auto">
                    <Button href={`/blog/${post.slug}`} variant="primary">
                      <Icon name="lucide:arrow-right" slot="icon-after" />
                      Baca Studi Kasus
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  </div>
</Layout>
