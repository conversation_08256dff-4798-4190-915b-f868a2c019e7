---
import Layout from "@/layouts/Layout.astro";
import HeroSection from "@/components/services/HeroSection.astro";
import BusinessFocusSection from "@/components/services/BusinessFocusSection.astro";
import BenefitsSection from "@/components/services/BenefitsSection.astro";
import FaqSection from "@/components/services/FaqSection.astro";
import CtaSection from "@/components/services/CtaSection.astro";
import ProcessSection from "@/components/services/ProcessSection.astro";
import CaseStudySection from "@/components/services/CaseStudySection.astro";
import PlusCardShowcase from "@/components/services/PlusCardShowcase.astro";

// Import Assets - Menggunakan gambar website yang sudah ada untuk sementara
import websiteCreationImage from "@/assets/pages/jasa-konversi-website-ke-blocks-wordpress.webp"; 
import ctaImagePlaceholder from "@/assets/pages/jasa-konversi-website-ke-blocks-wordpress.webp";
import caseStudyImage from "@/assets/blog/2025/April/Mei/harunstudio-after-astro.png";

const metaDescription = "Jasa migrasi website dari WordPress ke Astro. Dapatkan web 10x lebih cepat, score Core Web Vitals sempurna & keamanan lebih baik. Ideal untuk performa maksimal.";

// OG Image dan Alt Text
const ogImage = "/assets/pages/jasa-pembuatan-website.webp"; // Path relatif dari root publik
const ogImageAlt = "Migrasi WordPress ke Astro - Website 10x Lebih Cepat";

// JSON-LD untuk halaman jasa
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Jasa Migrasi WordPress ke Astro",
  "description": metaDescription,
  "provider": {
    "@type": "Organization",
    "name": "Harun Studio",
    "url": "https://harunstudio.com"
  },
  "serviceType": "Website Migration",
  "offers": {
    "@type": "Offer",
    "price": "7000000",
    "priceCurrency": "IDR"
  },
  "serviceOutput": {
    "@type": "WebSite",
    "name": "Website berbasis Astro",
    "description": "Website cepat dan aman dengan Core Web Vitals sempurna"
  },
  "areaServed": {
    "@type": "Country",
    "name": "Indonesia"
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://harunstudio.com/jasa/migrasi-wordpress-ke-astro"
  }
};

// --- DATA KONTEN UNTUK HALAMAN MIGRASI WORDPRESS KE ASTRO ---

// Data untuk Hero Section
const heroData = {
  title: "Jasa Migrasi dari WordPress ke Astro",
  subtitle: "Tingkatkan kecepatan website hingga 10x lebih cepat dengan teknologi Astro yang modern. Solusi ideal untuk website statis dengan performa maksimal.",
  primaryButtonText: "Konsultasikan Kebutuhan Anda",
  primaryButtonLink: "https://wa.me/*************?text=Halo%2C%20saya%20ingin%20bertanya%20tentang%20layanan%20migrasi%20WordPress%20ke%20Astro%20di%20Harun%20Studio.",
  secondaryButtonText: "Pelajari Lebih Lanjut",
  secondaryButtonLink: "#business-focus",
};

// Data untuk Business Focus Section
const businessFocusData = {
  title: "Apakah Anda frustrasi dengan lambatnya website WordPress?",
  subtitle: "ATAU ingin website statis yang super cepat? Astro adalah solusi modern dengan performa luar biasa yang cocok untuk konten-focused websites.",
  listItems: [
    "Performa website 10x lebih cepat",
    "Score Core Web Vitals sempurna (95+)",
    "Tanpa database yang rentan terhadap serangan",
    "Pengelolaan konten yang lebih sederhana dan developer-friendly"
  ],
  buttonText: "Lihat Keunggulan Astro",
  buttonLink: "#benefits",
  imageSrc: websiteCreationImage,
  imageAlt: "Migrasi dari WordPress ke Astro",
  showImageShadow: true,
  id: "business-focus"
};

// Data untuk Benefits Section
const benefitsData = {
  id: "benefits",
  title: "Keunggulan Website Berbasis Astro",
  description: "Teknologi Astro memberikan keunggulan signifikan dibanding WordPress untuk website statis:",
  benefits: [
    { 
      title: "Kecepatan Super Cepat", 
      description: "Website Astro memuat hampir instan dengan pendekatan zero-JS by default yang revolusioner.", 
      icon: "lucide:zap", 
      bullets: ["FCP < 0.5 detik", "TTI hampir instan", "Bundle JS minimal", "Partial Hydration"] 
    },
    
    { 
      title: "Keamanan Tanpa Kompromi", 
      description: "Tidak ada database = tidak ada serangan SQL injection. Website statis jauh lebih aman dari hacking.", 
      icon: "lucide:shield", 
      bullets: ["Tanpa backend yang rentan", "Tidak ada admin dashboard", "Zero-day exploit immunity", "Deploy ke CDN terenkripsi"] 
    },
    
    { 
      title: "SEO Sempurna", 
      description: "Score Core Web Vitals 100/100 dan struktur HTML semantik yang disukai mesin pencari.", 
      icon: "lucide:search", 
      bullets: ["Perfect Lighthouse score", "Struktur HTML clean", "Metadata teroptimasi", "Zero client-side JavaScript (opsional)"] 
    },
    
    { 
      title: "Biaya Hosting Minimal", 
      description: "Deploy ke CDN modern dengan biaya sangat rendah atau bahkan gratis.", 
      icon: "lucide:credit-card", 
      bullets: ["Netlify/Vercel free tier", "Cloudflare Pages", "GitHub Pages", "Tanpa biaya server mahal"] 
    },
    
    { 
      title: "Developer Experience Superior", 
      description: "Dukungan untuk React, Vue, Svelte, atau vanilla JavaScript dalam satu project.", 
      icon: "lucide:code", 
      bullets: ["Multiple framework support", "TypeScript built-in", "Hot module reloading", "Component-based architecture"] 
    },
    
    { 
      title: "Content Workflow Modern", 
      description: "Markdown/MDX native support dan integrasi headless CMS.", 
      icon: "lucide:file-text", 
      bullets: ["Markdown content authoring", "Integrasi dengan Contentful/Sanity/etc", "Git-based workflow", "Pemisahan konten & tampilan"] 
    }
  ],
  showPlusFeatures: true,
  plusFeaturesTitle: "Keunggulan Ekstra Migrasi ke Astro",
  plusFeaturesDescription: "Dengan migrasi ke Astro, Anda juga mendapatkan keuntungan tambahan berikut:",
  plusFeatures: [
    { 
      title: "Zero Vendor Lock-in", 
      description: "Tidak terikat platform tertentu, bebas pindah hosting kapanpun.", 
      icon: "lucide:unlock", 
      bullets: ["Open-source", "Standard web tech", "Portable codebase", "Multi-deploy target"] 
    },
    
    { 
      title: "Deployment Otomatis", 
      description: "CI/CD pipeline otomatis untuk deployment yang aman dan konsisten.", 
      icon: "lucide:git-branch", 
      bullets: ["Preview deployments", "Rollback instan", "Branch-based preview", "Deployment monitoring"] 
    },
    
    { 
      title: "Skalabilitas Tanpa Batas", 
      description: "Website Anda siap menangani traffic besar tanpa slowdown.", 
      icon: "lucide:trending-up", 
      bullets: ["CDN distribution", "Edge caching", "Infinite scalability", "No server bottlenecks"] 
    }
  ],
  showTestimonial: false
};

// Data untuk Process Section
const processData = {
  showCta: true,
  ctaBadge: "TEKNOLOGI MODERN",
  ctaTitle: "14-21 Hari untuk Website Modern yang Super Cepat",
  ctaSubtitle: "Jangan biarkan website lambat menghambat bisnis Anda. Astro memberikan pengalaman pengguna superior dengan kecepatan loading yang hampir instan.",
  ctaButtonLink: "https://wa.me/*************?text=Halo%2C%20saya%20ingin%20memulai%20proses%20migrasi%20WordPress%20ke%20Astro%20di%20Harun%20Studio.",
  ctaButtonText: "Migrasi Sekarang",
  ctaFootnote: "Durasi bisa bervariasi tergantung kompleksitas dan ukuran website",
  processTitle: "Bagaimana Proses Migrasi dari WordPress ke Astro?",
  processSubtitle: "Kami menggunakan pendekatan terstruktur untuk memastikan migrasi yang mulus dan hasil optimal.",
  steps: [
    { 
      number: 1, 
      title: "Audit Website Existing", 
      description: "Kami menganalisis struktur, konten, dan fitur website WordPress Anda saat ini.", 
      icon: "lucide:search", 
      details: "Tim kami mengaudit seluruh konten, fungsionalitas, dan kebutuhan teknis dari website WordPress yang akan dimigrasi untuk mengidentifikasi semua komponen yang perlu dipindahkan ke Astro.", 
      detailTags: [{icon: 'lucide:file-text', text: 'Content Mapping'}, {icon: 'lucide:layers', text: 'Feature Analysis'}] 
    },
    
    { 
      number: 2, 
      title: "Perencanaan Arsitektur", 
      description: "Merancang struktur Astro yang optimal sesuai kebutuhan spesifik website Anda.", 
      icon: "lucide:layout", 
      details: "Kami merancang arsitektur Astro yang efisien dengan fokus pada performa dan SEO, termasuk struktur routing, komponen, dan integrasi data yang diperlukan.", 
      detailTags: [{icon: 'lucide:columns', text: 'Component Design'}, {icon: 'lucide:git-fork', text: 'Routing Structure'}] 
    },
    
    { 
      number: 3, 
      title: "Konversi Konten", 
      description: "Memigrasikan semua konten dari WordPress ke format Markdown/MDX yang digunakan Astro.", 
      icon: "lucide:file-text", 
      details: "Seluruh konten website WordPress dikonversi dan distrukturkan ulang ke format Markdown/MDX yang optimal untuk Astro, dengan mempertahankan semua formatting, gambar, dan metadata.", 
      detailTags: [{icon: 'lucide:download', text: 'Content Extraction'}, {icon: 'lucide:refresh-cw', text: 'Markdown Conversion'}] 
    },
    
    { 
      number: 4, 
      title: "Development & Styling", 
      description: "Membangun ulang tampilan website dengan komponen Astro modern dan styling yang konsisten.", 
      icon: "lucide:code", 
      details: "Kami membangun ulang UI website menggunakan komponen Astro yang modular dan efisien, dengan styling yang konsisten menggunakan CSS modern atau framework seperti Tailwind CSS.", 
      detailTags: [{icon: 'lucide:layers', text: 'Component Development'}, {icon: 'lucide:palette', text: 'CSS/Tailwind'}] 
    },
    
    { 
      number: 5, 
      title: "Implementasi Interaktivitas", 
      description: "Menambahkan elemen interaktif seperlunya dengan pendekatan islands architecture.", 
      icon: "lucide:zap", 
      details: "Untuk fitur yang membutuhkan interaktivitas seperti form, slider, atau fitur dinamis lainnya, kami mengimplementasikan partial hydration dengan island architecture Astro yang revolusioner.", 
      detailTags: [{icon: 'lucide:server', text: 'Partial Hydration'}, {icon: 'lucide:puzzle', text: 'Interactive Islands'}] 
    },
    
    { 
      number: 6, 
      title: "Testing & Optimasi", 
      description: "Menguji website di berbagai perangkat dan mengoptimasi untuk performa maksimal.", 
      icon: "lucide:gauge", 
      details: "Website diuji secara menyeluruh di berbagai browser dan perangkat, dengan fokus pada optimasi Core Web Vitals dan user experience secara keseluruhan.", 
      detailTags: [{icon: 'lucide:bar-chart-2', text: 'Performance Testing'}, {icon: 'lucide:smartphone', text: 'Device Testing'}] 
    },
    
    { 
      number: 7, 
      title: "Deployment & Training", 
      description: "Melakukan deployment ke hosting modern dan memberikan training pengelolaan konten.", 
      icon: "lucide:rocket", 
      details: "Website Astro di-deploy ke platform modern seperti Netlify/Vercel/Cloudflare dengan setup CI/CD, dan kami memberikan training komprehensif untuk pengelolaan konten ke depannya.", 
      detailTags: [{icon: 'lucide:git-merge', text: 'CI/CD Setup'}, {icon: 'lucide:book-open', text: 'Documentation'}] 
    }
  ]
};

// Data untuk FAQ Section
const faqData = {
  title: "Pertanyaan Umum Seputar Migrasi ke Astro",
  subtitle: "Informasi penting yang mungkin Anda pertanyakan tentang proses migrasi dari WordPress ke Astro.",
  faqs: [
    { 
      question: "Apa itu Astro dan mengapa lebih baik dari WordPress?", 
      answer: "Astro adalah modern web framework yang fokus pada performa dan developer experience. Tidak seperti WordPress yang server-rendered dengan database, Astro menghasilkan website statis yang super cepat dengan partial hydration untuk interaktivitas. Hasilnya adalah website yang loading 10x lebih cepat, lebih aman, dan SEO-friendly."
    },
    
    { 
      question: "Apakah saya akan kehilangan fitur dari WordPress setelah migrasi?", 
      answer: "Astro sangat ideal untuk content-focused websites. Jika website Anda mengandalkan banyak plugin WordPress dinamis atau fitur e-commerce kompleks, mungkin akan ada trade-off. Namun, sebagian besar fitur dapat diimplementasikan dengan cara yang lebih modern dan performa lebih baik di Astro. Kami akan diskusikan kebutuhan spesifik Anda sebelum migrasi."
    },
    
    { 
      question: "Bagaimana dengan pengelolaan konten setelah migrasi?", 
      answer: "Ada beberapa opsi: 1) Menggunakan file Markdown/MDX langsung dengan Git workflow, cocok untuk developer/tech-savvy, 2) Mengintegrasikan headless CMS seperti Contentful, Sanity, atau Strapi untuk UI yang user-friendly, atau 3) Setup hybrid dengan WordPress sebagai headless CMS yang mengirim konten ke frontend Astro. Kami akan rekomendasikan solusi terbaik sesuai kebutuhan Anda."
    },
    
    { 
      question: "Berapa lama proses migrasi dari WordPress ke Astro?", 
      answer: "Timeframe bergantung pada kompleksitas website, jumlah halaman, dan fitur khusus. Website sederhana biasanya memerlukan 14-21 hari kerja, sementara website dengan ribuan konten atau fitur kompleks mungkin membutuhkan 30-45 hari. Kami akan memberikan timeline spesifik setelah audit awal."
    },
    
    { 
      question: "Apakah SEO website saya akan terpengaruh setelah migrasi?", 
      answer: "Justru sebaliknya! Website Astro biasanya mendapat peningkatan signifikan dalam Core Web Vitals, yang merupakan faktor ranking Google. Kami juga memastikan semua URL, metadata, dan struktur konten dipreservasi dengan redirects yang tepat untuk mempertahankan SEO equity yang sudah ada."
    },
    
    { 
      question: "Bagaimana dengan forms, comments, dan fitur interaktif lainnya?", 
      answer: "Astro mendukung 'islands of interactivity' dimana kita bisa menambahkan komponen interaktif seperti forms, comments, sliders, dll tanpa membuat seluruh halaman berat. Kami mengimplementasikan solusi seperti Formspree untuk forms, Disqus/Giscus untuk comments, atau custom API endpoints untuk fungsi spesifik lainnya."
    },
    
    { 
      question: "Apakah saya masih bisa mengedit konten dengan mudah seperti di WordPress?", 
      answer: "Ya, tergantung setup yang dipilih. Dengan headless CMS, pengalaman editing konten bisa sama intuitifnya dengan WordPress, bahkan dengan preview langsung. Untuk setup berbasis Git, ada tools seperti Netlify CMS yang memberikan interface visual untuk editing Markdown."
    },
    
    { 
      question: "Apa perbedaan utama Astro dengan Gatsby atau Next.js?", 
      answer: "Astro memiliki pendekatan unik yaitu 'zero JavaScript by default', yang berarti hanya mengirimkan JavaScript yang benar-benar diperlukan ke browser. Gatsby dan Next.js selalu mengirimkan bundle React, meskipun halaman statis. Astro juga framework-agnostic, sehingga Anda bisa menggunakan React, Vue, Svelte, atau kombinasinya dalam satu project. Hasilnya adalah performa yang jauh lebih baik tanpa mengorbankan developer experience."
    }
  ],
  contactText: "Tertarik untuk migrasi ke Astro? Ada pertanyaan lainnya?",
  contactButtonText: "Konsultasi Gratis",
  contactButtonLink: "https://wa.me/*************?text=Halo%2C%20saya%20ingin%20konsultasi%20tentang%20layanan%20migrasi%20WordPress%20ke%20Astro%20di%20Harun%20Studio."
};

// Data untuk CTA Section
const ctaData = {
  badge: "Upgrade Website Anda",
  title: "Siap Bermigrasi ke Teknologi Web Modern? <br class=\"hidden md:block\" /> Mulai Sekarang",
  subtitle: "Konsultasikan kebutuhan migrasi website Anda dan dapatkan penawaran khusus untuk early adopters Astro.",
  secondaryButtonText: "Konsultasi Gratis",
  secondaryButtonLink: "https://wa.me/*************?text=Halo%20Harun%20Studio%2C%20saya%20tertarik%20dengan%20jasa%20migrasi%20WordPress%20ke%20Astro.%20Bisakah%20kita%20berdiskusi%3F",
  imageSrc: ctaImagePlaceholder,
  imageAlt: "Migrasi dari WordPress ke Astro",
  showImage: true
};
---

<Layout 
  title="Jasa Migrasi WordPress ke Astro | Website 10x Lebih Cepat" 
  description={metaDescription}
  ogImage={ogImage}
  ogImageAlt={ogImageAlt}
  ogType="service"
  jsonLd={jsonLd}
>
  <HeroSection {...heroData} />
  <BusinessFocusSection {...businessFocusData} />
  <BenefitsSection {...benefitsData} showPlusFeatures={false} />
  
  {/* Plus Features dengan desain baru */}
  <div class="py-12 md:py-16 lg:py-20">
    <PlusCardShowcase 
      title={benefitsData.plusFeaturesTitle}
      description={benefitsData.plusFeaturesDescription}
      features={benefitsData.plusFeatures}
    />
  </div>
  
  <CaseStudySection 
    title="Studi Kasus Sukses" 
    subtitle="Lihat bagaimana kami menerapkan migrasi dari WordPress ke Astro untuk website kami sendiri"
    caseTitle="Studi Kasus: Migrasi Harun Studio dari WordPress ke Astro"
    caseDescription="Bagaimana kami meningkatkan kecepatan loading website 8x lipat, mengurangi biaya hosting 100%, dan menyederhanakan workflow dengan memigrasikan dari WordPress ke Astro.js."
    imageSrc={caseStudyImage}
    imageAlt="Harun Studio setelah migrasi ke Astro.js"
    metrics={[
      {
        icon: "lucide:zap",
        value: "8x",
        label: "Lebih cepat loading"
      },
      {
        icon: "lucide:dollar-sign",
        value: "100%",
        label: "Penghematan hosting"
      },
      {
        icon: "lucide:trending-down",
        value: "75%",
        label: "Pengurangan ukuran"
      },
      {
        icon: "lucide:activity",
        value: "95+",
        label: "Core Web Vitals"
      }
    ]}
  />
  <ProcessSection {...processData} />
  <FaqSection {...faqData} />
  <CtaSection {...ctaData} />
</Layout> 