---
import Layout from "@/layouts/Layout.astro";
import QuickHero from "@/components/quick-services/QuickHero.astro";
import BenefitsSection from "@/components/services/BenefitsSection.astro";
import QuickProcess from "@/components/quick-services/QuickProcess.astro";
import QuickFAQ from "@/components/quick-services/QuickFAQ.astro";
import QuickCTA from "@/components/quick-services/QuickCTA.astro";
import PlusCardShowcase from "@/components/services/PlusCardShowcase.astro";
import migrasiWebsiteImage from '@/assets/pages/jasa-migrasi-website.webp';

const metaDescription = "Jasa migrasi website WordPress tanpa downtime (<1 jam). Pindahkan website Anda antar hosting/VPS dengan aman, tanpa kehilangan data atau SEO. Dijamin berhasil.";

// OG Image dan Alt Text
const ogImage = "/og-jasa-test/jasa-migrasi-website.webp";
const ogImageAlt = "Jasa Migrasi Website WordPress Cepat dan Tanpa Downtime";

// JSON-LD untuk halaman jasa
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Jasa Migrasi Website WordPress Tanpa Downtime",
  "description": metaDescription,
  "provider": {
    "@type": "Organization",
    "name": "Harun Studio",
    "url": "https://harunstudio.com"
  },
  "serviceType": "Website Migration",
  "offers": {
    "@type": "Offer",
    "price": "250000",
    "priceCurrency": "IDR"
  },
  "areaServed": {
    "@type": "Country",
    "name": "Indonesia"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "5",
    "bestRating": "5",
    "worstRating": "1",
    "reviewCount": "7",
    "itemReviewed": {
      "@type": "Product",
      "name": "Jasa Migrasi Website WordPress Tanpa Downtime",
      "image": "https://harunstudio.com/assets/pages/jasa-migrasi-website.webp",
      "description": "Layanan migrasi website WordPress tanpa downtime, selesai dalam < 1 jam. Pindahkan website antar hosting atau ke VPS dengan aman, tanpa kehilangan data dan SEO.",
      "brand": {
        "@type": "Brand",
        "name": "Harun Studio"
      },
      "sku": "02",
      "offers": {
        "@type": "Offer",
        "price": "250000",
        "priceCurrency": "IDR",
        "availability": "https://schema.org/InStock",
        "priceValidUntil": "2025-12-31"
      }
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://harunstudio.com/jasa/migrasi-website"
  },
  "serviceOutput": {
    "@type": "WebSite",
    "name": "Website WordPress yang dimigrasikan",
    "description": "Website WordPress yang telah dimigrasikan ke hosting/server baru dengan aman, tanpa kehilangan data dan SEO."
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Layanan Migrasi Website",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Migrasi Antar Hosting",
          "description": "Migrasi website antar hosting dengan berbagai control panel (cPanel, DirectAdmin, Plesk)"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Migrasi ke VPS",
          "description": "Migrasi dari shared hosting ke VPS dengan setup LEMP stack optimal"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Migrasi ke Cloud Panel",
          "description": "Migrasi ke platform cloud modern (Cloudways, RunCloud, GridPane)"
        }
      }
    ]
  },
  "hasFAQPage": {
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Apakah proses migrasi akan mempengaruhi peringkat SEO website saya?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Tidak, kami memastikan bahwa proses migrasi tidak akan mempengaruhi peringkat SEO Anda. Kami mempertahankan semua struktur URL, meta data, dan elemen penting dari website Anda selama migrasi. Kami juga menerapkan pengalihan 301 yang tepat jika diperlukan untuk memastikan tidak ada perubahan dalam peringkat atau visibilitas di mesin pencari."
        }
      },
      {
        "@type": "Question",
        "name": "Bagaimana Anda menjamin tidak ada kehilangan data selama migrasi?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Keamanan data adalah prioritas utama kami. Sebelum memulai proses migrasi, kami selalu membuat backup lengkap dari website Anda. Kami menggunakan teknik migrasi bertahap yang memungkinkan verifikasi data di setiap langkah. Jika terjadi masalah tak terduga, kami dapat mengembalikan website Anda ke kondisi sebelumnya dengan cepat."
        }
      },
      {
        "@type": "Question",
        "name": "Apakah website saya akan tetap online selama proses migrasi?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ya, layanan migrasi kami dirancang dengan metode zero-downtime. Website Anda akan tetap online dan dapat diakses selama migrasi berlangsung. Kami menggunakan pendekatan parallel provisioning di mana situs baru dibangun dan diuji secara paralel sebelum kami mengalihkan traffic, memastikan pengalaman yang mulus bagi pengunjung website Anda."
        }
      },
      {
        "@type": "Question",
        "name": "Berapa lama waktu yang dibutuhkan untuk migrasi website WordPress?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Untuk sebagian besar website WordPress, proses migrasi biasanya selesai dalam waktu kurang dari 1 jam. Namun, waktu yang tepat dapat bervariasi tergantung pada ukuran website, jumlah data, kompleksitas konfigurasi, dan jenis migrasi. Pada konsultasi awal, kami akan memberikan estimasi waktu yang lebih akurat berdasarkan kebutuhan spesifik Anda."
        }
      },
      {
        "@type": "Question",
        "name": "Apa itu setup LEMP stack dalam layanan migrasi ke VPS?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "LEMP stack adalah kombinasi dari Linux, Nginx (dibaca Engine-X), MySQL/MariaDB, dan PHP yang merupakan lingkungan server optimal untuk WordPress. Dalam layanan migrasi ke VPS, kami mengkonfigurasi server dengan LEMP stack yang dioptimalkan khusus untuk WordPress dengan pengaturan cache, keamanan, dan performa terbaik sesuai kebutuhan website Anda."
        }
      },
      {
        "@type": "Question",
        "name": "Apa yang perlu saya persiapkan sebelum migrasi dimulai?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Untuk memulai proses migrasi, kami memerlukan beberapa informasi dan akses penting: (1) Akses login ke control panel hosting lama dan baru, (2) Kredensial FTP/SFTP jika tersedia, (3) Akses admin WordPress, (4) Informasi domain jika akan ada perubahan DNS. Tim kami akan membimbing Anda melalui semua yang diperlukan saat konsultasi awal."
        }
      }
    ]
  }
};

// Data untuk QuickHero - Copywriting yang lebih powerful
const heroData = {
  title: "Jasa Migrasi Website WordPress - Cepat & Tanpa Downtime",
  subtitle: "Kami siap memindahkan website Anda ke hosting/server baru dengan aman, tanpa kehilangan data, dan tanpa downtime. SEO tetap terjaga!",
  icon: "lucide:cloud-upload",
  timeframe: "Migrasi selesai dalam waktu < 1 jam",
  primaryButtonText: "Mulai Migrasi Website",
  primaryButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20tertarik%20dengan%20layanan%20migrasi%20website%20di%20Harun%20Studio.",
  secondaryButtonText: "Pelajari Lebih Lanjut",
  secondaryButtonLink: "#benefits",
};

// Data untuk BenefitsSection (sebelumnya QuickBenefits) - Copywriting yang ditingkatkan
const benefitsData = {
  id: "benefits",
  title: "Solusi Migrasi Website Profesional untuk Berbagai Kebutuhan",
  description: "Pindahkan website WordPress Anda ke hosting atau server baru dengan cepat, aman, dan tanpa kehilangan traffic atau pelanggan Anda. Kami menangani berbagai jenis migrasi dengan jaminan keberhasilan 100%.",
  price: "Rp250.000",
  benefits: [
    {
      title: "Migrasi Antar Control Panel",
      description: "Pindahkan website Anda antar hosting dengan berbagai control panel populer tanpa kehilangan data, konfigurasi, atau pengaturan penting lainnya.",
      icon: "lucide:server",
      bullets: ["cPanel ke cPanel", "cPanel ke DirectAdmin", "Plesk ke cPanel", "Migrasi antar hosting berbeda"],
      price: "Rp250.000",
      pricePeriod: "per website"
    },
    {
      title: "Migrasi ke/dari VPS",
      description: "Tingkatkan performa dengan migrasi ke VPS atau sebaliknya. Kami juga membantu setup server VPS Anda dengan stack LEMP yang teroptimasi khusus untuk WordPress.",
      icon: "lucide:cpu",
      bullets: ["Shared hosting ke VPS", "Setup LEMP stack optimal", "Konfigurasi Nginx + PHP-FPM", "Optimasi keamanan & performa"],
      price: "Rp350.000",
      pricePeriod: "per website"
    },
    {
      title: "Migrasi ke Cloud Panel",
      description: "Pindahkan website Anda ke platform cloud modern yang menawarkan kemudahan pengelolaan, skalabilitas, dan performa tinggi untuk WordPress.",
      icon: "lucide:cloud",
      bullets: ["Cloudways", "RunCloud", "GridPane", "SpinupWP", "Serverpilot"],
      price: "Rp450.000",
      pricePeriod: "per website"
    }
  ],
  showPlusFeatures: true,
  plusFeaturesTitle: "Keunggulan Eksklusif Layanan Migrasi Harun Studio",
  plusFeaturesDescription: "Setiap layanan migrasi kami didukung oleh keunggulan berikut:",
  plusFeatures: [
    {
      title: "Zero Downtime Migration",
      description: "Website Anda tetap online dan dapat diakses pengunjung selama proses migrasi berlangsung, tanpa kehilangan traffic atau pelanggan potensial.",
      icon: "lucide:activity",
      bullets: ["Migrasi tanpa downtime", "Website tetap online", "Pengalihan DNS mulus", "Prosedur paralel"]
    },
    {
      title: "100% SEO & Data Safety",
      description: "Migrasi dilakukan dengan mempertahankan struktur URL, SEO, dan keamanan data website Anda secara menyeluruh. Tidak ada kehilangan peringkat di Google.",
      icon: "lucide:shield-check",
      bullets: ["Struktur URL tetap utuh", "Peringkat SEO terjaga", "Backup lengkap otomatis", "Validasi integritas data"]
    },
    {
      title: "Dukungan Pasca-Migrasi",
      description: "Dukungan teknis berlanjut selama 30 hari setelah migrasi selesai untuk memastikan website Anda berjalan optimal di lingkungan barunya.",
      icon: "lucide:headphones",
      bullets: ["Support 30 hari", "Bantuan konfigurasi server", "Panduan pengelolaan", "Konsultasi teknis"]
    }
  ],
  showTestimonial: false,
};

// Data untuk QuickProcess - Copywriting yang lebih detail
const processData = {
  title: "3 Langkah Mudah Proses Migrasi Website WordPress",
  subtitle: "Kami memastikan proses migrasi website WordPress Anda berjalan lancar, efisien, dan tanpa downtime dengan proses yang transparan dan terorganisir.",
  steps: [
    {
      title: "Konsultasi & Analisis",
      description: "Diskusi dengan tim kami untuk menentukan kebutuhan spesifik, jenis migrasi, dan rencana proses yang optimal untuk website Anda.",
      icon: "lucide:message-square",
      bullets: ["Analisis kebutuhan teknis", "Penentuan timeline migrasi", "Pengumpulan akses yang diperlukan"],
      subtext: "Menyusun strategi migrasi yang paling tepat untuk website Anda."
    },
    {
      title: "Proses Migrasi",
      description: "Tim ahli kami melakukan migrasi dengan metode zero-downtime. Kami memindahkan semua data, file, dan konfigurasi dengan teliti untuk memastikan tidak ada yang hilang.",
      icon: "lucide:arrow-right-left",
      bullets: ["Backup data menyeluruh", "Transfer file & database", "Migrasi konfigurasi & pengaturan"],
      subtext: "Ketelitian tinggi untuk memastikan tidak ada data yang hilang."
    },
    {
      title: "Verifikasi & Pengujian",
      description: "Setelah migrasi selesai, kami melakukan serangkaian pengujian menyeluruh untuk memastikan semua berfungsi sempurna di lingkungan baru.",
      icon: "lucide:check-circle",
      bullets: ["Validasi konten & fungsi", "Verifikasi keamanan", "Pengecekan performa & SEO"],
      subtext: "Memastikan website berjalan sempurna di hosting/server barunya."
    }
  ],
  cta: {
    text: "Mulai Migrasi Sekarang",
    url: "https://wa.me/6281291274023?text=Halo%2C%20saya%20tertarik%20dengan%20layanan%20migrasi%20website%20di%20Harun%20Studio."
  }
};

// Data untuk QuickFAQ - Pertanyaan yang lebih menjawab kekhawatiran calon klien
const faqData = {
  title: "Pertanyaan Umum tentang Migrasi Website",
  subtitle: "Temukan jawaban untuk pertanyaan-pertanyaan umum seputar layanan migrasi website kami.",
  faqs: [
    {
      question: "Apakah proses migrasi akan mempengaruhi peringkat SEO website saya?",
      answer: "Tidak, kami memastikan bahwa proses migrasi tidak akan mempengaruhi peringkat SEO Anda. Kami mempertahankan semua struktur URL, meta data, dan elemen penting dari website Anda selama migrasi. Kami juga menerapkan pengalihan 301 yang tepat jika diperlukan untuk memastikan tidak ada perubahan dalam peringkat atau visibilitas di mesin pencari."
    },
    {
      question: "Bagaimana Anda menjamin tidak ada kehilangan data selama migrasi?",
      answer: "Keamanan data adalah prioritas utama kami. Sebelum memulai proses migrasi, kami selalu membuat backup lengkap dari website Anda. Kami menggunakan teknik migrasi bertahap yang memungkinkan verifikasi data di setiap langkah. Jika terjadi masalah tak terduga, kami dapat mengembalikan website Anda ke kondisi sebelumnya dengan cepat."
    },
    {
      question: "Apakah website saya akan tetap online selama proses migrasi?",
      answer: "Ya, layanan migrasi kami dirancang dengan metode zero-downtime. Website Anda akan tetap online dan dapat diakses selama migrasi berlangsung. Kami menggunakan pendekatan parallel provisioning di mana situs baru dibangun dan diuji secara paralel sebelum kami mengalihkan traffic, memastikan pengalaman yang mulus bagi pengunjung website Anda."
    },
    {
      question: "Berapa lama waktu yang dibutuhkan untuk migrasi website WordPress?",
      answer: "Untuk sebagian besar website WordPress, proses migrasi biasanya selesai dalam waktu kurang dari 1 jam. Namun, waktu yang tepat dapat bervariasi tergantung pada ukuran website, jumlah data, kompleksitas konfigurasi, dan jenis migrasi. Pada konsultasi awal, kami akan memberikan estimasi waktu yang lebih akurat berdasarkan kebutuhan spesifik Anda."
    },
    {
      question: "Apa itu setup LEMP stack dalam layanan migrasi ke VPS?",
      answer: "LEMP stack adalah kombinasi dari Linux, Nginx (dibaca Engine-X), MySQL/MariaDB, dan PHP yang merupakan lingkungan server optimal untuk WordPress. Dalam layanan migrasi ke VPS, kami mengkonfigurasi server dengan LEMP stack yang dioptimalkan khusus untuk WordPress dengan pengaturan cache, keamanan, dan performa terbaik sesuai kebutuhan website Anda."
    },
    {
      question: "Apa yang perlu saya persiapkan sebelum migrasi dimulai?",
      answer: "Untuk memulai proses migrasi, kami memerlukan beberapa informasi dan akses penting: (1) Akses login ke control panel hosting lama dan baru, (2) Kredensial FTP/SFTP jika tersedia, (3) Akses admin WordPress, (4) Informasi domain jika akan ada perubahan DNS. Tim kami akan membimbing Anda melalui semua yang diperlukan saat konsultasi awal."
    }
  ],
  contactText: "Punya pertanyaan khusus?",
  contactButtonText: "Konsultasi Gratis",
  contactButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20bertanya%20tentang%20layanan%20migrasi%20website%20di%20Harun%20Studio."
};

// Data untuk QuickCTA - Lebih persuasif
const ctaData = {
  badge: "MULAI MIGRASI SEKARANG",
  title: "Tingkatkan Performa Website Anda dengan Migrasi yang Aman",
  subtitle: "Pindahkan website WordPress Anda tanpa risiko downtime, kehilangan data, atau penurunan SEO. Proses cepat, dan aman.",
  buttonText: "Mulai Migrasi Website Hari Ini",
  buttonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20memulai%20migrasi%20website%20saya%20dengan%20Harun%20Studio.",
  showImage: true,
  imageSrc: migrasiWebsiteImage,
  imageAlt: "Jasa Migrasi Website WordPress"
};
---

<Layout 
  title="Jasa Migrasi Website WordPress (<1 Jam Tanpa Downtime)" 
  description={metaDescription}
  ogImage={ogImage}
  ogImageAlt={ogImageAlt}
  ogType="service"
  jsonLd={jsonLd}
>
  <QuickHero {...heroData} />
  <BenefitsSection {...benefitsData} showPlusFeatures={false} />
  
  {/* Plus Features dengan desain baru */}
  <div class="py-12 md:py-16 lg:py-20">
    <PlusCardShowcase 
      title={benefitsData.plusFeaturesTitle}
      description={benefitsData.plusFeaturesDescription}
      features={benefitsData.plusFeatures}
      price={benefitsData.price}
      pricePeriod={benefitsData.pricePeriod}
    />
  </div>
  
  <QuickProcess {...processData} />
  <QuickFAQ {...faqData} />
  <QuickCTA {...ctaData} />
</Layout> 