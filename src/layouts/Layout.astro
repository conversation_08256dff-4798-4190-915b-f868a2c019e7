---
import { SEO } from "astro-seo";
import Footer from "@/components/footer.astro";
import Navbar from "@/components/navbar/navbar.astro";
import "@fontsource-variable/plus-jakarta-sans";
import "../styles/global.css";
import FloatingWhatsApp from "../components/FloatingWhatsApp.astro";

export interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
  ogImageAlt?: string;
  ogType?: string;
  jsonLd?: any;
}

const canonicalURL = new URL(Astro.url.pathname, Astro.site).toString();

const resolvedImageWithDomain = new URL(
  "/opengraph.jpg",
  Astro.site
).toString();

const {
  title,
  description,
  ogImage,
  ogImageAlt = "Harun Studio",
  ogType = "website",
  jsonLd
} = Astro.props;

// Final image URL: Use custom OG image if provided, otherwise use default
const finalOgImage = ogImage
  ? new URL(ogImage, Astro.site).toString()
  : resolvedImageWithDomain;

const siteName = "Harun Studio";
const siteDescription = "Layanan Website Untuk Bisnis yang Serius";
const makeTitle = title
  ? title.length > 45
    ? title  // Judul panjang: gunakan judul lengkap, tanpa brand
    : `${title} | ${siteName}`       // Judul pendek: tambahkan brand
  : `${siteName} - ${siteDescription}`; // Fallback jika title TIDAK ADA
const pageDescription = description && description.trim() !== '' ? description : siteDescription;

---

<!doctype html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet" />

    <!-- Umami Analytics -->
    <script 
    defer 
    src="/stats.js" 
    data-website-id="c95cfefa-f4d3-40af-8b7c-afb48e2bab04"
    data-host-url="/umami-api">
  </script>

    <!-- WhatsApp Link Tracking Script -->
    <script>
      // TypeScript declaration for Umami
      declare global {
        interface Window {
          umami?: {
            track: (eventName: string, eventData?: any) => void;
          };
        }
      }
      
      document.addEventListener('DOMContentLoaded', function() {
        // Get current page path for tracking
        const currentPath = window.location.pathname;
        
        // Find all links that contain wa.me
        const whatsappLinks = document.querySelectorAll('a[href*="wa.me"]');
        
        // Add click event listeners to each WhatsApp link
        whatsappLinks.forEach(link => {
          // Skip if already has Umami tracking attribute (to respect existing tracking)
          if (link.hasAttribute('data-umami-event')) {
            // Already has Umami tracking, don't add duplicate tracking
            return;
          }
          
          // Skip if already processed by this script
          if (link.getAttribute('data-umami-tracked') === 'true') return;
          
          // Mark as tracked by this script
          link.setAttribute('data-umami-tracked', 'true');
          
          // Get button name from text content or nearby elements
          let buttonName = 'whatsapp-link'; // Default fallback name
          
          // Try to get button name from the link text
          if (link.textContent && link.textContent.trim()) {
            buttonName = link.textContent.trim();
          } 
          // Try to get from aria-label
          else if (link.getAttribute('aria-label')) {
            const ariaLabel = link.getAttribute('aria-label');
            if (ariaLabel) buttonName = ariaLabel;
          }
          // If still no name, try to find a nearby heading
          else {
            const container = link.closest('div, section');
            if (container) {
              const nearestHeading = container.querySelector('h1, h2, h3, h4, h5, h6');
              if (nearestHeading && nearestHeading.textContent) {
                buttonName = nearestHeading.textContent.trim();
              }
            }
          }
          
          // Create a safe button name for tracking
          // Keep only alphanumeric characters and hyphens, limit length
          const safeButtonName = buttonName.toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '') // Remove special characters
            .substring(0, 20); // Limit length to 20 chars
          
          // Get a safe page identifier (simplified)
          // Extract just the service name without hyphens
          let serviceName = '';
          const pathParts = currentPath.split('/');
          if (pathParts.length > 2 && pathParts[1] === 'jasa') {
            // For /jasa/* pages, use a simplified service identifier
            serviceName = pathParts[2].split('-')[0]; // Take just the first word
          } else {
            // For other pages, use a simple identifier
            serviceName = 'page';
          }
          
          // Create a very short event name to avoid API errors
          const eventName = `wa-${safeButtonName}-${serviceName}`;
          
          // Add click event listener
          link.addEventListener('click', function(e) {
            // Check if umami is available
            if (typeof window.umami !== 'undefined') {
              window.umami.track(eventName);
            } else {
              console.log('Umami tracking attempted but not available:', eventName);
            }
          });
        });
      });
    </script>

    <!-- <link rel="preload" as="image" href={src} alt="Hero" /> -->
    <SEO
      title={makeTitle}
      description={pageDescription}
      canonical={canonicalURL}
      noindex={false}
      nofollow={false}
      twitter={{
        creator: "@harunstudio",
        site: "@harunstudio",
        card: "summary_large_image",
      }}
      openGraph={{
        basic: {
          url: canonicalURL,
          type: ogType,
          title: makeTitle,
          image: finalOgImage,
        },
        image: {
          alt: ogImageAlt,
          width: 1200,
          height: 630,
        },
        optional: {
          siteName: siteName,
          description: pageDescription,
          locale: "id_ID",
        }
      }}
      extend={{
        meta: [
          { name: "theme-color", content: "#2652E3" },
          { name: "apple-mobile-web-app-capable", content: "yes" },
          { name: "apple-mobile-web-app-status-bar-style", content: "black" },
        ],
        link: [
          { rel: "manifest", href: "/site.webmanifest" },
          { rel: "author", href: "/humans.txt" },
        ],
      }}
    />

    <!-- Structured Data Slot -->
    <slot name="structured-data" />
    {jsonLd && (
      <script type="application/ld+json" set:html={JSON.stringify(jsonLd)} />
    )}
  </head>
  <body>
    <Navbar />
    <slot />
    <Footer />
    <FloatingWhatsApp mobile="6281291274023" />
    <style is:global>
      /* Improve Page speed */
      /* https://css-tricks.com/almanac/properties/c/content-visibility/ */
      img {
        content-visibility: auto;
      }
    </style>
    <!-- Site-wide background pattern -->
    <div class="fixed inset-0 -z-5 pointer-events-none" style="mix-blend-mode: multiply;">
      <!-- 10 segmented vertical lines -->
      <div class="absolute h-full" style="left: calc(100% / 10 * 1); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 2); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 3); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 4); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 5); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 6); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 7); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 8); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
      <div class="absolute h-full" style="left: calc(100% / 10 * 9); width: 1px; background-image: repeating-linear-gradient(to bottom, var(--color-gray-100), var(--color-gray-200) 15px, transparent 15px, transparent 20px);"></div>
    </div>
  </body>
</html>
