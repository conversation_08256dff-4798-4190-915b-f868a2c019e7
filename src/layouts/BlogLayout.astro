---
import Container from "@/components/container.astro";
import { getFormattedDate } from "@/utils/all";
import Layout from "./Layout.astro";
import { Icon } from "astro-icon/components";

const { frontmatter } = Astro.props;
// Memastikan description tidak undefined dan bukan string kosong
const description = frontmatter.description && frontmatter.description.trim() !== '' 
  ? frontmatter.description 
  : `Artikel tentang ${frontmatter.title} oleh ${frontmatter.author}`;
---

<Layout title={frontmatter.title} description={description}>
  <div class="relative hero-padding bg-gradient-to-b from-gray-50 to-white">
    <div class="absolute inset-0 -z-10">
      <div class="absolute -left-40 -top-40 w-96 h-96 rounded-full bg-[var(--color-brand-50)] opacity-70 blur-3xl"></div>
      <div class="absolute right-0 top-10 w-72 h-72 rounded-full bg-[var(--color-brand-50)] opacity-50 blur-3xl"></div>
    </div>

    <Container>
      <div class="mx-auto max-w-3xl">
        <div class="flex items-center gap-2 mb-5">
          <span class="px-3 py-1 bg-[var(--color-brand-50)] rounded-full text-xs font-medium text-[var(--color-brand)]">
            {frontmatter.category}
          </span>
          <time class="text-gray-500 text-sm" datetime={frontmatter.publishDate}>
            {getFormattedDate(frontmatter.publishDate)}
          </time>
        </div>
        
        <h1 class="text-4xl lg:text-5xl font-bold lg:tracking-tight mt-1 lg:leading-tight">
          {frontmatter.title}
        </h1>
        
        <div class="flex items-center mt-6 mb-8 border-b border-gray-100 pb-6">
          <div class="w-12 h-12 rounded-full bg-[var(--color-brand-50)] flex items-center justify-center text-[var(--color-brand)]">
            <Icon name="lucide:user" class="w-6 h-6" />
          </div>
          <div class="ml-3">
            <p class="font-medium text-gray-900">{frontmatter.author}</p>
            <p class="text-sm text-gray-500">Penulis Artikel</p>
          </div>
        </div>
      </div>
    </Container>
  </div>

  <Container>
    <div class="mx-auto max-w-3xl">
      <div class="bg-white rounded-xl shadow-sm p-8 md:p-10 mb-12 prose prose-lg prose-headings:font-bold prose-headings:text-gray-900 prose-p:text-gray-700 prose-img:rounded-xl prose-a:text-[var(--color-brand)] max-w-none">
        <slot />
      </div>
      
      <div class="border-t border-gray-100 pt-6 flex flex-wrap gap-3 mb-16">
        {frontmatter.tags.map((tag) => (
          <span class="px-3 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-700 hover:bg-[var(--color-brand-50)] hover:text-[var(--color-brand)] transition-colors">
            #{tag}
          </span>
        ))}
      </div>
      
      <div class="flex justify-center mb-16">
        <a href="/blog" class="inline-flex items-center px-5 py-3 bg-[var(--color-brand-50)] text-[var(--color-brand)] font-medium rounded-lg hover:bg-[var(--color-brand-100)] transition-colors">
          <Icon name="lucide:arrow-left" class="w-4 h-4 mr-2" />
          Kembali ke Blog
        </a>
      </div>
    </div>
  </Container>
</Layout>
