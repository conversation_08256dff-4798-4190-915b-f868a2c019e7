---
draft: false
title: "Membangun ImageTools.pro dengan Next.js dan Vibe Coding"
description: "Bagaimana saya membangun app pengolah gambar ImageTools.pro hanya dalam 5 hari dengan bantuan AI dan Next.js. Cerita lengkap dari konsep hingga deployment."
snippet: "Pengalaman membangun ImageTools.pro, aplikasi pengolah gambar online dengan teknologi Next.js, Sharp.js, dan Shadcn UI, serta bagaimana AI merevolusi cara kita membangun aplikasi web."
image: {
    src: "https://cdn.harunstudio.com/2025/May/imagetools-pro.webp",
    alt: "ImageTools.pro - Website pengolah gambar online"
}
publishDate: "2025-05-13"
category: "PENGEMBANGAN"
author: "Willya Randika"
tags: ["next.js", "ai", "web app", "pengembangan", "sharp.js", "shadcn", "vercel", "vibe coding"]
---

Bayangkan membangun aplikasi web yang kompleks tanpa harus menghabiskan berminggu-minggu atau berbulan-bulan. Saya baru saja melakukannya dengan [ImageTools.pro](https://imagetools.pro) - sebuah aplikasi pengolah gambar online yang saya bangun hanya dalam waktu 5 hari dengan bantuan AI dan teknologi modern.

Project ini adalah hasil dari apa yang dikenal sebagai "vibe coding" - pendekatan pengembangan software yang dipopulerkan oleh Andrej Karpathy pada awal tahun 2025. Menurut [Wikipedia](https://en.wikipedia.org/wiki/Vibe_coding), vibe coding adalah metode pengembangan software yang mengandalkan AI, di mana developer mendeskripsikan masalah dalam beberapa kalimat sebagai prompt kepada large language model (LLM), kemudian membimbing dan menyempurnakan kode yang dihasilkan.

Mari saya ceritakan bagaimana saya menggunakan pendekatan ini untuk membangun ImageTools.pro dalam waktu super singkat.

## Mengapa Membangun ImageTools.pro?

Ide untuk membuat ImageTools.pro berawal dari kebutuhan pribadi saya sendiri. Saya sering harus mengkompres, mengubah ukuran, dan mengkonversi gambar untuk berbagai proyek, dan saya tidak puas dengan alat-alat yang tersedia. Banyak di antaranya:

1. **Lambat dan penuh iklan** - Mengganggu alur kerja
2. **Memproses gambar di server** - Masalah privasi karena gambar harus diupload
3. **Antarmuka yang membingungkan** - Sulit menemukan fitur yang dibutuhkan
4. **Banyak batasan di versi gratis** - Seperti jumlah file atau ukuran maksimum

Saya ingin membuat aplikasi yang:
- Mengutamakan pemrosesan di browser (client-side) kapanpun memungkinkan
- Menyediakan antarmuka yang bersih dan intuitif
- Sepenuhnya gratis tanpa batasan yang mengganggu
- Cepat dan efisien
- Menawarkan berbagai alat pengolahan gambar di satu platform

## Perencanaan dengan AI

Langkah pertama yang saya lakukan adalah menggunakan Claude 3.7 Sonnet untuk membuat roadmap komprehensif dan daftar tugas untuk proyek ini. Saya menjelaskan visi saya tentang aplikasi pengolah gambar yang berjalan di browser, dan AI membantuku:

1. Menentukan tech stack optimal untuk proyek ini
2. Merekomendasikan library spesifik seperti Sharp.js untuk pemrosesan gambar
3. Mengidentifikasi fitur-fitur minimum viable product (MVP)
4. Menyusun langkah-langkah implementasi yang logis

Roadmap dan daftar tugas ini kemudian saya berikan kepada Cursor (editor berbasis AI) untuk "dipelajari" sebagai konteks, sehingga saat saya mulai membangun, AI sudah memiliki pemahaman mendalam tentang tujuan proyek saya.

## Tech Stack Pilihan

Berdasarkan rekomendasi AI dan pengalaman pribadi saya, saya memilih stack teknologi berikut:

**Next.js 15.2.4** - Versi terbaru dengan App Router, memberikan pengalaman developer luar biasa dengan fitur-fitur server components, routing berbasis file, dan optimisasi gambar bawaan.

**React 19** - Core library untuk membangun UI interaktif dengan semua fitur terbaru.

**TypeScript** - Untuk type safety dan developer experience yang lebih baik.

**Shadcn UI** - Komponen UI yang sangat customizable dan dibangun di atas Radix UI, memberikan fondasi desain yang solid tanpa overhead dari library UI besar.

**Tailwind CSS** - Utility-first CSS framework yang mempercepat styling tanpa meninggalkan fleksibilitas.

**Framer Motion** - Untuk animasi yang mulus dan interaktif di seluruh antarmuka.

Untuk pemrosesan gambar, saya menggunakan kombinasi beberapa library:

**Browser-based Processing** (utama):
- `browser-image-compression` (v2.0.2) - Untuk kompresi gambar di browser
- `image-conversion` (v2.1.1) - Untuk konversi format dasar
- `jimp` (v1.6.0) - Library manipulasi gambar JavaScript murni
- `react-image-crop` dan `react-easy-crop` - Untuk UI editing gambar yang intuitif
- Canvas API browser - Untuk operasi dasar seperti rotate/flip

**Server-side Processing** (opsional/terbatas):
- `sharp` (v0.34.0) - Library pemrosesan gambar berbasis Node.js untuk operasi lebih kompleks
- `bmp-js` - Untuk penanganan format BMP
- `jspdf` - Untuk konversi ke PDF
- `to-ico` - Untuk pembuatan favicon

Saya juga menggunakan beberapa utility libraries:
- Zustand untuk state management yang sederhana dan efektif
- Lodash untuk fungsi-fungsi utility
- React Dropzone untuk UI upload file yang user-friendly
- Lucide React untuk ikon-ikon yang konsisten dan modern

## Proses Pengembangan: Mengalir dengan Vibe Coding

Berbeda dengan pendekatan tradisional yang lebih terstruktur, proses pengembangan ImageTools.pro mengalir secara alami—benar-benar mencerminkan esensi dari vibe coding. Untungnya, pengalaman saya dengan HTML dan CSS sangat membantu dalam memahami dan mengarahkan kode yang dihasilkan AI.

Alih-alih bekerja dalam tahapan-tahapan yang jelas terpisah, saya bergerak bolak-balik antara berbagai aspek pengembangan, tergantung pada inspirasi dan kebutuhan saat itu. Berikut beberapa highlight dari proses tersebut:

### Scaffolding dan Struktur Dasar

Saya mulai dengan meminta Cursor + Claude 3.7 untuk menyiapkan struktur proyek Next.js dengan Tailwind CSS dan Shadcn UI. Pengalaman ini sangat mulus—saya cukup mendeskripsikan apa yang saya inginkan, dan AI menghasilkan kode boilerplate dengan cepat.

```typescript
// app/page.tsx - Halaman utama sederhana
import Hero from '@/components/landing/Hero'
import FeaturedTools from '@/components/landing/FeaturedTools'
import HowItWorks from '@/components/landing/HowItWorks'
import Features from '@/components/landing/Features'

export default function Home() {
  return (
    <main>
      <Hero />
      <FeaturedTools />
      <HowItWorks />
      <Features />
    </main>
  )
}
```

### Implementasi Pemrosesan Gambar

Tantangan terbesar yang saya hadapi adalah mengimplementasikan pemrosesan gambar yang efisien. Setelah melakukan riset, saya memutuskan untuk mengadopsi pendekatan hybrid yang memprioritaskan browser:

1. **Pemrosesan Browser (Client-side)** - Ini adalah pendekatan utama saya. Untuk mayoritas operasi, saya menggunakan kombinasi dari Canvas API browser dan library JavaScript seperti `browser-image-compression`, `image-conversion` dan `jimp`. Ini memungkinkan pengguna untuk memproses gambar langsung di browser tanpa perlu upload ke server.

   Contoh implementasi untuk resize gambar dengan Canvas API:
   
   ```javascript
   function resizeImage(file, maxWidth, maxHeight) {
     return new Promise((resolve) => {
       const img = new Image();
       img.onload = () => {
         // Menghitung dimensi baru
         let width = img.width;
         let height = img.height;
         
         if (width > maxWidth) {
           height = (maxWidth / width) * height;
           width = maxWidth;
         }
         
         if (height > maxHeight) {
           width = (maxHeight / height) * width;
           height = maxHeight;
         }
         
         // Rendering ke canvas
         const canvas = document.createElement('canvas');
         canvas.width = width;
         canvas.height = height;
         
         const ctx = canvas.getContext('2d');
         ctx.drawImage(img, 0, 0, width, height);
         
         // Konversi canvas ke Blob
         canvas.toBlob((blob) => {
           resolve(blob);
         }, file.type);
       };
       
       img.src = URL.createObjectURL(file);
     });
   }
   ```

2. **Pemrosesan Server (Fallback)** - Untuk operasi yang lebih kompleks atau konversi format tertentu yang tidak bisa dilakukan secara efisien di browser (seperti konversi AVIF atau TIFF), saya menggunakan API serverless dengan Sharp.js. Yang penting, file pengguna ditangani dengan aman dan tidak disimpan setelah konversi selesai.

   Saya memastikan untuk meminimalkan penggunaan pemrosesan server dan hanya menggunakannya saat benar-benar diperlukan, seperti yang tercantum di halaman About website saya:
   
   ```
   "All conversions happen directly in your browser. Your files never leave your device."
   ```

Pendekatan hybrid yang memprioritaskan browser ini memberikan saya fleksibilitas untuk menawarkan lebih banyak fitur sambil tetap menjaga privasi pengguna sebagai prioritas utama.

Claude dan Gemini sangat membantu saya dalam mengintegrasikan berbagai library pemrosesan gambar dan mengoptimalkan performa browser. Misalnya, untuk image compression, saya sempat bingung antara menggunakan `browser-image-compression` atau implementasi manual dengan Canvas, dan AI membantu saya membandingkan kelebihan dan kekurangan masing-masing pendekatan.

### Antarmuka Pengguna dan UX

Pengembangan UI menggunakan Shadcn UI dan Tailwind CSS berjalan sangat lancar. Untuk membuat pengalaman pengguna lebih menarik, saya menambahkan animasi dengan Framer Motion dan memastikan semua interaksi terasa responsif.

![ImageTools.pro interface](https://cdn.harunstudio.com/2025/May/ui-imagetools.webp)

Saya juga memanfaatkan Zustand untuk state management yang ringan, yang memungkinkan saya mengelola state aplikasi tanpa boilerplate yang berlebihan. Ini sangat berguna untuk menyimpan pengaturan pengguna saat beralih antar tool yang berbeda.

```typescript
// Store untuk status pemrosesan dan pengaturan pengguna
const useImageStore = create((set) => ({
  originalImage: null,
  processedImage: null,
  processingStatus: 'idle', // 'idle' | 'processing' | 'done' | 'error'
  settings: {
    quality: 80,
    format: 'webp',
    resize: { width: 800, height: 600, maintain: true },
  },
  setOriginalImage: (file) => set({ originalImage: file }),
  setProcessedImage: (result) => set({ processedImage: result }),
  setProcessingStatus: (status) => set({ processingStatus: status }),
  updateSettings: (newSettings) => set((state) => ({
    settings: { ...state.settings, ...newSettings }
  })),
  resetStore: () => set({
    originalImage: null,
    processedImage: null,
    processingStatus: 'idle',
  }),
}));
```

### Testing dan Deployment

Di fase akhir pengembangan, saya fokus pada:

1. Testing pada berbagai browser dan perangkat
2. Optimisasi performa dan aksesibilitas
3. Pengaturan analitik tanpa cookie
4. Deployment ke Vercel

Proses deployment ke Vercel berjalan sangat mulus. Hanya perlu beberapa klik untuk menghubungkan repositori GitHub dan melakukan deployment produksi pertama.

## Koleksi Lengkap 35+ Tools untuk Pengolahan Gambar

Salah satu hal yang saya banggakan dari ImageTools.pro adalah koleksi lengkap lebih dari [35 alat pengolahan gambar yang saya buat](https://imagetools.pro/all-tools). Semua tools ini dirancang untuk memenuhi berbagai kebutuhan pengguna, dan saya mengorganisirnya dalam kategori yang mudah diakses:

### 1. Konversi Format

Saya mengimplementasikan dukungan konversi antara semua format populer:
- PNG ke JPG, WebP, AVIF, PDF, BMP, TIFF
- JPG ke PNG, WebP, AVIF, PDF, BMP, TIFF
- WebP ke PNG, JPG, AVIF, PDF, BMP, TIFF
- AVIF ke PNG, JPG, WebP, TIFF
- TIFF ke PNG, JPG, WebP, AVIF
- BMP ke PNG, JPG, WebP, AVIF, PDF

### 2. Kompresi Gambar

Tools yang saya buat untuk mengoptimalkan ukuran file berbagai format:
- Compress Any Image (format apa pun)
- Compress PNG (mempertahankan transparansi)
- Compress JPG (dengan pengaturan kualitas yang dapat disesuaikan)
- Compress WebP (optimasi lanjutan untuk format modern)

### 3. Resize dan Cropping

Tools untuk mengubah dimensi gambar:
- Resize Any Image (semua format)
- Resize PNG, JPG, WebP (dengan optimasi khusus format)
- Crop Image (dengan preset aspect ratio atau crop bebas)

### 4. Editing dan Tools Khusus

Tools untuk kebutuhan khusus yang saya tambahkan:
- Rotate & Flip Image
- Image Adjustments (Grayscale, Tint, dll)
- Add Watermark
- Image Compositor (menggabungkan beberapa gambar)
- Favicon Maker
- Image Placeholder Maker
- Image to Base64 / Base64 to Image

Mengimplementasikan semua tools ini tentu saja jadi tantangan tersendiri, terutama karena beberapa memerlukan logika pemrosesan yang kompleks. Tapi dengan pendekatan vibe coding, saya bisa menyelesaikan mayoritas fungsi dalam waktu singkat. Rasanya seperti punya asisten developer yang bekerja non-stop bersama saya!

## Bagaimana Kinerja ImageTools.pro Saat Ini?

Setelah saya luncurkan, ImageTools.pro menunjukkan hasil yang membuat saya senang:

- **Skor PageSpeed hampir sempurna** (95+ untuk mobile dan 100 untuk desktop)

![ImageTools.pro page PageSpeed](https://cdn.harunstudio.com/2025/May/pagespeed-imagetools-pro.webp)
- **Waktu pemrosesan gambar yang cepat** (rata-rata kurang dari 2 detik untuk gambar 1MB)
- **Feedback pengguna yang positif** tentang UI dan kecepatan

Yang lebih membanggakan lagi, ini menjadi bukti nyata kesuksesan pengembangan berbasis AI. Apa yang biasanya membutuhkan waktu berminggu-minggu, saya selesaikan dalam 5 hari.

## Pelajaran dari Membangun ImageTools.pro

### 1. Kekuatan AI dalam Development

Pengalaman ini benar-benar menunjukkan betapa AI telah merevolusi proses pengembangan software. Claude 3.7 Sonnet dan Gemini 2.5 Pro tidak hanya membantu saya menulis kode, tetapi juga:

- Melakukan brainstorming fitur dan arsitektur
- Membantu debug masalah kompleks
- Menyarankan optimasi
- Menjawab pertanyaan teknis yang biasanya memerlukan penelusuran Stack Overflow

Bekerja dengan AI terasa seperti pair programming dengan developer senior yang selalu siap membantu saya.

### 2. Pendekatan Hybrid Processing

Meskipun awalnya saya berencana untuk membangun aplikasi 100% client-side, saya menyadari bahwa pendekatan hybrid seringkali lebih praktis dan efisien. Beberapa konversi format dan operasi kompleks memang lebih baik dilakukan di server.

Pendekatan hybrid yang saya pilih memberikan beberapa keuntungan:

- **Fleksibilitas lebih besar** - Saya bisa menawarkan lebih banyak fitur dan format konversi
- **Kinerja lebih baik** - Beberapa operasi kompleks jauh lebih cepat di server
- **Tetap menjaga privasi** - Dengan memastikan file tidak disimpan setelah konversi
- **Keseimbangan optimal** - Antara kemampuan pemrosesan dan pengalaman pengguna

### 3. Next.js + Shadcn adalah Kombinasi Powerful

Gabungan Next.js dan Shadcn UI terbukti sangat efektif untuk rapid development:

- **Struktur proyek yang intuitif** dari Next.js
- **Komponen yang highly customizable** dari Shadcn
- **Developer experience yang luar biasa** dengan keduanya

### 4. Performa vs. Fitur

Tantangan terbesar adalah menyeimbangkan performa dengan fitur. WebAssembly dan worker threads membantu mempertahankan UI yang responsif, tetapi ada batasan seberapa banyak pengolahan yang dapat dilakukan di browser.

Saya harus mengorbankan beberapa fitur kompleks (seperti pengolahan batch untuk banyak gambar) untuk iterasi pertama demi menjaga performa yang baik.

## Rencana untuk ImageTools.pro

Sementara versi pertama sudah live, ada banyak fitur yang ingin saya tambahkan:

1. **Pemrosesan batch** untuk menangani banyak gambar sekaligus
2. **Lebih banyak filter dan efek** menggunakan WebGL untuk performa yang lebih baik
3. **Pembuatan GIF dan animasi** dari rangkaian gambar
4. **Progressive Web App (PWA)** untuk penggunaan offline
5. **Alat AI** seperti penghapusan background otomatis (mengintegrasikan model TensorFlow.js)
6. **Dukungan WebP dan AVIF** yang lebih baik untuk format gambar modern
7. **WebAssembly** untuk meningkatkan performa pemrosesan gambar kompleks di browser

## Vibe Coding: Revolusi dalam Pengembangan Software

ImageTools.pro adalah contoh nyata dari kekuatan vibe coding, istilah yang dipopulerkan oleh Andrej Karpathy pada Februari 2025. Menurut [artikel di Medium oleh Madhukar Kumar](https://madhukarkumar.medium.com/a-comprehensive-guide-to-vibe-coding-tools-2bd35e2d7b4f), vibe coding telah menjadi trend signifikan dalam dunia pengembangan software, dengan beragam tools yang bermunculan untuk mendukung metodologi ini.

Pendekatan vibe coding memberikan beberapa keunggulan penting:

1. **Mengutamakan kecepatan** tanpa mengorbankan kualitas
2. **Memanfaatkan AI** sebagai copilot, bukan hanya sebagai alat
3. **Fokus pada pengalaman pengguna** dari awal
4. **Iteratif dan eksperimental** tanpa rasa takut gagal

Dengan AI, saya sebagai developer dapat bergerak dari konsep ke produk jadi dalam waktu yang jauh lebih singkat dari sebelumnya. Ini membuka kemungkinan baru untuk eksperimen dan inovasi yang sebelumnya terhambat oleh batasan waktu dan sumber daya.

## Kesimpulan

ImageTools.pro adalah bukti bahwa kita berada di era baru pengembangan software. Dengan bantuan AI dan pendekatan vibe coding, saya bisa membangun aplikasi web yang kompleks dengan lebih dari 35 alat pengolahan gambar dalam hitungan hari, bukan minggu atau bulan.

Jika Anda ingin mencoba ImageTools.pro, kunjungi [https://imagetools.pro](https://imagetools.pro) dan coba sendiri. Saya merancangnya dengan pendekatan hybrid yang menyeimbangkan pemrosesan browser dan server, memberikan pengalaman yang cepat, aman, dan fungsional.

Anda juga dapat [menghubungi saya](/hubungi-kami) jika tertarik untuk membangun aplikasi web serupa atau ingin berdiskusi lebih lanjut tentang pengalaman vibe coding dengan AI. Saya percaya ini adalah masa depan pengembangan software, dan saya senang berbagi pengalaman dan insight tentang perjalanan ini.