---
draft: false
title: "Redesain Toko Online Muslimadani - Hasil & Proses"
description: "<PERSON><PERSON> proses dan hasil redesain toko online Muslimadani yang meningkatkan konversi dan pengalaman pengguna. Pelajari strateginya!"
snippet: "Studi kasus redesain toko online Muslimadani.id yang meningkatkan kecepatan loading dan konversi penjualan."
image: {
    src: "../../assets/images/mockup-toko-online-muslimadani.webp",
    alt: "Mockup Toko Online Muslimadani"
}
publishDate: "2024-06-20"
category: "STUDI KASUS"
author: "Willya Randika"
tags: ["woocommerce", "redesain", "optimasi", "konversi", "generatepress"]
---

Pernahkah Anda merasa frustrasi dengan performa website Anda sendiri?

Begitulah yang dirasakan oleh tim di [Muslimadani.id](https://Muslimadani.id), sebuah brand toko online yang menjual baju muslim pria. Mereka mengeluhkan kecepatan loading website yang lambat, terutama ketika pelanggan mencoba mengklik tombol add to cart di halaman produk. Ini jelas mengurangi tingkat konversi penjualan online mereka.

Setelah menghubungi beberapa vendor, mereka akhirnya memutuskan untuk bekerja sama dengan kami di Harun Studio. Diskusi panjang melalui WhatsApp dan panggilan telepon membantu saya memahami kedalaman masalah yang dihadapi.

Saya pun mulai melakukan audit teknis dan menemukan beberapa penyebab utama dari lambatnya kecepatan toko online mereka. Dengan pemahaman ini, saya merancang strategi untuk meningkatkan kecepatan loading.

Singkat cerita, setelah implementasi berbagai optimasi, hasilnya mulai terlihat. Kecepatan loading website meningkat secara signifikan, dan ini memberikan pengalaman yang lebih baik bagi para pelanggan Muslimadani.id.

Kepuasan mereka terhadap hasil awal ini membuka pintu bagi diskusi lebih lanjut tentang peningkatan-peningkatan lainnya, seperti SEO, tingkat konversi, dan lain-lain.

Saya kemudian melakukan audit menyeluruh terhadap toko online mereka dan menemukan bahwa masih banyak ruang yang bisa ditingkatkan.

Dari hasil audit ini, Muslimadani.id memutuskan untuk mengambil langkah yang lebih besar: melakukan redesain total website mereka dengan fokus pada performa dan tingkat konversi.

Perjalanan transformasi ini melibatkan langkah-langkah strategis seperti pemilihan tema baru yang lebih ringan dan responsif, konversi dari Elementor ke Blocks, pengurangan jumlah plugin yang tidak perlu, serta migrasi hosting ke VPS (Managed by Harun Studio) yang lebih andal. Setiap langkah diambil dengan hati-hati untuk memastikan bahwa pengalaman pengguna menjadi lebih baik dan tingkat konversi meningkat.

Hasilnya? Website yang lebih cepat, lebih responsif, dan lebih siap untuk memenuhi kebutuhan pelanggan Muslimadani.id. Redesain ini bukan hanya tentang tampilan baru, tetapi juga tentang memberikan pengalaman berbelanja yang lebih baik bagi pengguna, yang pada akhirnya mendukung pertumbuhan bisnis Muslimadani.id.

## Identifikasi Masalah: Bagaimana Saya Mengungkap Hambatan-hambatan di Toko Online Muslimadani.id Sebelumnya

Mengapa [Muslimadani.id](https://Muslimadani.id) sepakat untuk memulai proyek redesain? Mereka ingin memanfaatkan berbagai channel iklan seperti Google Ads, Facebook, dan Instagram untuk menjaring lebih banyak traffic ke toko online mereka.

Namun, saya mengingatkan bahwa jika desain toko online tidak berfokus pada tingkat konversi penjualan, iklan yang dikampanyekan tidak akan efektif dalam menarik calon pembeli dan mengubah mereka menjadi pelanggan.

Untuk itu, saya memulai dengan melakukan audit menyeluruh terhadap situs mereka. Saya meninjau setiap halaman—dari homepage, halaman shop, hingga halaman produk, cart, dan checkout. Saya juga menganalisis konten dan mengidentifikasi solusi untuk setiap masalah yang ada.

Tentu saja, saya tidak bisa memaparkan seluruh laporan audit dalam studi kasus ini. Jadi, mari kita fokus pada satu contoh penting: audit halaman produk pada tampilan desktop.

![Hal Produk Muslimadani Before](../../assets/images/Hal-produk-muslimadani-before-scaled.webp "Hal-produk-muslimadani-before")

Setelah melakukan peninjauan mendalam, saya menemukan setidaknya tujuh masalah utama dalam desain halaman produk sebelumnya:

1. **Tombol "Tambah ke Keranjang"** beserta opsi "Pilih Warna" dan "Pilih Ukuran" berada di bagian paling bawah deskripsi produk, bukan di bagian atas seperti yang umumnya digunakan. Ini dapat menurunkan konversi penjualan.

3. **Pilih Varian**: Pelanggan perlu mengklik dropdown "Pilih sebuah opsi" terlebih dahulu untuk melihat warna dan ukuran yang tersedia.

5. **Varian Warna**: Hanya menampilkan warna sebagai teks saja tanpa menggunakan gambar atau warna yang sebenarnya. Sebagai contoh, pengguna mungkin kesulitan membedakan warna maroon dengan merah gelap hanya dengan melihat teks "maroon" dan "dark red".

7. **Deskripsi Produk**: Menggunakan warna font yang kurang terang, terlihat kurang rapi, dan terlalu panjang (seharusnya bisa dimunculkan sebagian saja supaya tidak terlalu ramai).

9. **Informasi Produk**: Tidak ada informasi seperti apakah ini produk baru, diskon, trending, atau best seller, yang bisa membantu pelanggan memutuskan untuk melakukan pembelian.

11. **Kepercayaan Pelanggan**: Tidak ada informasi yang bisa menambah kepercayaan pelanggan, seperti info pengiriman (bayar di tempat, pengiriman seluruh Indonesia, gratis tukar ukuran, dan refund jika tidak sesuai).

13. **Secure Transaction**: Tidak ada informasi "Transaksi Aman" di dekat tombol "Tambahkan ke Keranjang" yang bisa menambah rasa percaya diri pelanggan untuk melakukan pemesanan.

Dan berikut halaman produk setelah di redesain:

![Halaman Produk After Muslimadani](../../assets/images/halaman-produk-after-muslimadani.png "halaman-produk-after-muslimadani")

Terlihat perbedaannya?

- Apakah Anda juga menyadari ada panah kiri dan kanan di foto produk serta galeri produk yang terlihat di sebelah kiri foto produk?

- Kemudian juga tombol "Tambah ke Tas Belanja" yang lebih cocok ketimbang "Add to Cart", dan terlihat paling menonjol dari keseluruhan elemen website (kecuali tombol WhatsApp)?

- Serta informasi singkat tentang stok produk yang saya custom dengan text "Bergegaslah, hanya tersisa 2 dalam stok!" (jika stok kurang dari 5) atau "Hanya tersisa 1 stok untuk varian ini. Pesan segera sebelum keduluan yang lain." jika stok hanya tersisa 1.

Saya juga memastikan untuk mempertahankan brand color dan tipografi yang telah mereka gunakan sebelumnya di channel-channel media sosial mereka, ke seluruh halaman website. Ini membantu menjaga konsistensi brand dan memberikan pengalaman yang lebih kohesif bagi pelanggan.

## Pembangungan Ulang Tema yang Berfokus pada Kecepatan

### Menggunakan GeneratePress dan GenerateBlocks

Untuk mencapai performa maksimal, saya memutuskan untuk mengkonversi website yang sebelumnya menggunakan tema dari ThemeForest dan page builder Elementor ke tema GeneratePress yang dikombinasikan dengan GenerateBlocks.

GeneratePress adalah tema WordPress yang ringan dan cepat, sementara GenerateBlocks memberikan fleksibilitas dalam desain tanpa mengorbankan kecepatan. Seluruh desain yang sebelumnya saya buat di Figma, kami konversikan ke WooCommerce mereka menggunakan kombinasi kuat ini. Hasilnya adalah sebuah website yang tidak hanya indah secara visual, tetapi juga responsif dan cepat.

### Kustomisasi Kode

Untuk memenuhi kebutuhan unik Muslimadani.id, saya menulis lebih dari 1000 baris kode kustom. Kode ini mencakup fitur-fitur khusus seperti informasi ukuran baju (S, M, L, XL, XXL...) di card produk, custom pricing di halaman produk, dan berbagai elemen lain yang mendukung pengalaman belanja yang lebih baik bagi pelanggan.

![Kustomisasi Kode](../../assets/images/kustomisasi-kode.png "kustomisasi-kode")

### Memindahkan Website dari Shared Hosting Niagahoster ke VPS SpeedyPage

Agar website memiliki performa tertinggi dan dapat menangani trafik yang tinggi dengan lancar, saya melakukan migrasi hosting dari Niagahoster shared hosting ke VPS dari SpeedyPage dengan spesifikasi berikut:

- AMD Ryzen 7900,
- DDR 5 Memory,
- NVMe storage
- lokasi server di Singapura.

Saya mengoptimalkan VPS ini menggunakan LEMP Stack (Linux, Nginx, MySQL, PHP). Dengan konfigurasi khusus, saya bisa memaksimalkan performa WooCommerce dan memastikan bahwa website Muslimadani.id berjalan dengan cepat dan stabil.

### Mengapa Perlu Dipindahkan ke VPS?

Ada banyak faktor selain karena memang dalam [layanan paket maintenance website](https://harunstudio.com/jasa-maintenance-website/) kami, semua klien kami tempatkan dalam hosting VPS berperforma tinggi, tapi berbicara soal kecepatan secara lebih spesifik, pengaruhnya adalah ke TTFB.

Sebelumnya, Muslimadani.id menggunakan shared hosting dengan beberapa keterbatasan. Shared hosting berbagi sumber daya dengan banyak website lain, yang dapat menyebabkan lambatnya Time to First Byte (TTFB). TTFB adalah waktu yang diperlukan oleh browser untuk menerima byte pertama dari server setelah permintaan dikirimkan.

TTFB yang lambat dapat mempengaruhi berbagai metrik kecepatan website, seperti:

- **First Contentful Paint (FCP)**: Waktu yang dibutuhkan untuk menampilkan konten pertama pada layar.
- **Largest Contentful Paint (LCP)**: Waktu yang dibutuhkan untuk menampilkan elemen terbesar di layar.
- **Total Blocking Time (TBT)**: Waktu di mana pengguna tidak dapat berinteraksi dengan halaman karena JavaScript dan CSS yang memblokir.
- **Cumulative Layout Shift (CLS)**: Stabilitas tata letak halaman saat dimuat.

Dengan memindahkan hosting ke VPS, kita mendapatkan sumber daya yang didedikasikan khusus untuk website [Muslimadani.id](http://Muslimadani.id). Ini secara signifikan mengurangi TTFB dan meningkatkan performa keseluruhan website. Hasilnya adalah pengalaman pengguna yang lebih cepat dan lebih responsif, yang pada akhirnya dapat meningkatkan tingkat konversi dan kepuasan pelanggan.

## Kesimpulan: Transformasi yang Berhasil

Dengan konversi dari Elementor ke GeneratePress dan GenerateBlocks serta redesain yang lebih ramah pembeli, Muslimadani.id berhasil meningkatkan kecepatan website dan pengalaman pengguna secara keseluruhan.

Kecepatan yang lebih baik tidak hanya meningkatkan pengalaman browsing, tetapi juga berdampak positif pada tingkat konversi penjualan.

Pelanggan kini dapat menjelajahi produk dan menyelesaikan pembelian dengan lebih cepat dan mudah, yang pada akhirnya meningkatkan kepuasan dan loyalitas mereka.

![Testimonial Muslimadani](../../assets/images/testimonial-muslimadani.png "testimonial-muslimadani")

Lihat [layanan redesain website kami](https://harunstudio.com/jasa/pembuatan-website/) atau [layanan konversi website ke blok WordPress](/jasa/konversi-website-ke-blocks/) kami, atau [jadwalkan konsultasi gratis .](https://harunstudio.com/hubungi-kami/)
