---
draft: false
title: "Transformasi Kecepatan Website WIN Equipment - St<PERSON> Kasus"
description: "Bagaimana WIN Equipment meningkatkan kecepatan website mereka? Simak studi kasus lengkap dan tips optimasi di sini!"
snippet: "Studi kasus peningkatan kecepatan website WIN Equipment dari skor 27 menjadi 92 di PageSpeed Insights."
image: {
    src: "../../assets/images/studi-kasus-win-equipment.co_.id_.webp",
    alt: "Studi Kasus Win Equipment Speed Optimization"
}
publishDate: "2024-06-11"
category: "STUDI KASUS"
author: "Willya Randika"
tags: ["woocommerce", "redesain", "optimasi", "konversi", "generatepress"]
---

Ada saat-saat di mana kita dihadapkan pada tantangan yang tampak menjengkelkan, namun justru di situlah kemampuan kita diuji.

Bayangkan sebuah akhir pekan yang seharusnya tenang, ketika saya menerima permintaan yang cukup mendesak dari [Win Equipment](https://win-equipment.co.id). Mereka membutuhkan bantuan untuk memindahkan website mereka dari hosting lama CloudNow ke Niagahoster shared hosting.

Proses migrasi seharusnya berjalan kurang dari 1 jam, tapi proyek singkat ini ternyata lebih menantang dari yang saya bayangkan. File backup berukuran cukup besar, tidak ada akses SSH pada hosting lama dan proses compress file backup di direktori public\_html juga beberapa kali mengalamai kegagalan, belum lagi kecepatan download yang lambat.

Pada akhirnya saya berhasil memindahkan website mereka di hari yang sama. Keberhasilan ini memberi kesempatan kepada Win Equipment untuk bertanya apa yang bisa ditingkatkan dari website mereka.

_Saya menjelaskan bahwa website mereka lambat, terdapat banyak error teknis SEO, dan konten yang tidak teroptimasi dengan baik dalam sebuah meeting online._

  
Dengan tujuan untuk memberikan pengalaman pengguna yang lebih baik dan meningkatkan peringkat SEO, Win Equipment memutuskan untuk memulai perbaikan secara bertahap, dan langkah pertama adalah meningkatkan kecepatan loading website mereka.

Mulailah perjalanan transformasi ini dengan pendekatan komprehensif yang melibatkan pergantian tema ke GeneratePress Premium, konversi dari WP Bakery ke Blocks, pengurangan plugin, migrasi hosting ke VPS termasuk optimasi VPS yang pada akhirnya membawa perubahan signifikan pada kinerja dan stabilitas website Win Equipment.

## Mengapa Speed Penting?

Di era digital saat ini, kecepatan website adalah aspek krusial yang tidak boleh diabaikan. Pengguna internet mengharapkan informasi yang cepat dan mudah diakses. Jika website Anda terlalu lambat, pengunjung cenderung meninggalkannya dan beralih ke alternatif lain yang lebih responsif.

Win Equipment menghadapi masalah serius terkait kecepatan. Dengan skor PageSpeed Insights hanya 27 untuk mobile dan 36 untuk desktop, serta gagal dalam core web vitals, website mereka membutuhkan waktu yang lama untuk dimuat.

![Before Win Equipment PageSpeed Score](../../assets/images/before-win-equipment-1.png "Skor PageSpeed Win Equipment Sebelum Optimasi")

Ini bukan hanya masalah kenyamanan pengguna, namun juga memiliki dampak besar pada beberapa aspek kunci dari kehadiran online websitemereka:

1. Pengalaman Pengguna (User Experience):
    - Kesan Pertama yang Buruk: Website yang lambat meninggalkan kesan pertama yang buruk pada pengunjung. Jika halaman tidak dimuat dalam beberapa detik pertama, pengunjung cenderung menutup tab dan mencari informasi di tempat lain.
    - Interaksi yang Berkurang: Web yang lambat membuat pengunjung enggan untuk mengeksplorasi lebih lanjut, mengurangi waktu yang dihabiskan di situs dan interaksi dengan konten.

3. Peringkat SEO:
    - Penurunan Peringkat di Mesin Pencari: Google menggunakan kecepatan web sebagai salah satu faktor dalam algoritma peringkat mereka. Website yang lambat akan cenderung ditempatkan lebih rendah dalam hasil pencarian, mengurangi visibilitas dan lalu lintas organik.
    - Core Web Vitals: Metrik ini mengukur elemen seperti waktu muat, interaktivitas, dan stabilitas visual. Gagal dalam metrik ini berarti bahwa website Anda dianggap tidak menyediakan pengalaman yang optimal.

5. Konversi dan Retensi Pengunjung:
    - Menurunkan Tingkat Konversi: Waktu muat yang lambat dapat membuat pengunjung frustrasi dan meninggalkan proses pembelian atau pendaftaran, yang berdampak langsung pada tingkat penjualan.
    - Retensi Pengunjung yang Rendah: Pengunjung yang mengalami website lambat cenderung tidak kembali. Kecepatan loading yang baik meningkatkan kemungkinan pengunjung kembali di masa depan.

Memahami pentingnya kecepatan website adalah langkah pertama untuk memperbaikinya. Dengan pendekatan komprehensif yang melibatkan pergantian tema ke GeneratePress Premium, konversi dari WP Bakery ke Blocks, pengurangan plugin, dan migrasi ke VPS termasuk optimasi VPS, kami berhasil membawa perubahan signifikan pada kinerja dan stabilitas website Win Equipment.

## Pendekatan dan Metodologi

### Analisis Awal

Saya memulai dengan audit performa komprehensif untuk mengidentifikasi semua masalah yang mempengaruhi kecepatan website. Analisis ini mencakup pemeriksaan tema, plugin yang tidak diperlukan, kode yang tidak efisien, dan konfigurasi hosting yang tidak optimal.

### Strategi yang Digunakan

Saya menerapkan beberapa strategi utama untuk mencapai tujuan proyek:

- Mengganti page builder dari WP Bakery ke GenerateBlocks / Blocks.
- Membangun ulang semua halaman dengan GenerateBlocks dan GeneratePress.
- Mengurangi jumlah plugin yang tidak digunakan dan mengganti beberapa plugin yang lambat atau dengan security yang rendah ke plugin alternatif yang lebih baik, tanpa mengurangi fungsi atau fitur penting.

- [Memindahkan hosting dari shared hosting ke VPS](https://harunstudio.com/jasa/migrasi-website/) dengan konfigurasi LEMP stack yang dioptimalkan maksimal untuk website WordPress

### Layanan yang Dibutuhkan

1. [Layanan setup VPS](https://harunstudio.com/jasa/maintenance-website/)
2. [Layanan konversi website ke blok WordPress](https://harunstudio.com/jasa/konversi-website-ke-blocks/)

## Implementasi

### Langkah-Langkah Utama

#### 1\. **Migrasi Tema dan Page Builder**:

Saya mengganti tema website dari Structure ke GeneratePress Premium. Langkah ini diambil karena GeneratePress adalah tema yang sangat ringan, dan berfokus pada kecepatan. Saya menggunakan tema ini di semua website yang saya miliki dan semua website klien.

- Tema tercepat di platform WordPress saat ini
- Salah satu tema populer yang telah digunakan lebih dari 5 juta website WordPress
- Merupakan tema yang diciptakan oleh developer yang sudah sangat dikenal di komunitas WordPress dan sudah berusia 10 tahun
- Lebih ringan dengan kode yang lebih bersih.
- Lebih sedikit CSS dan JavaScript yang perlu dimuat.
- Kemudahan dalam penyesuaian dan pengembangan lebih lanjut.
- Dapat dimodifikasi seperti apapun untuk kebutuhan apapun, jadi hampir dipastikan tidak perlu adanya pergantian tema lagi di masa yang akan datang.

#### 2\. **Pembangunan Ulang Halaman dengan GenerateBlocks**:

Saya membangun ulang semua halaman menggunakan GenerateBlocks dan GeneratePress, mulai dari homepage, pages, shop page, single product, blog & blogpost, dan semua archive.

Pendekatan ini memungkinkan pengurangan jumlah plugin dan eliminasi kode yang tidak perlu, sehingga website menjadi lebih ringan dan cepat.

<figure>

![GenerateBlocks Pattern Feature](../../assets/images/blocks.png "Fitur Pattern di GenerateBlocks")

<figcaption>

_Salah satu keunggulan menggunakan Blocks adalah fitur pattern, yang memungkinkan sinkronisasi semua blok di seluruh halaman. Dengan fitur ini, setiap kali kita mengedit salah satu pattern, perubahan tersebut akan secara otomatis diterapkan ke semua halaman yang menggunakan pattern yang sama._

</figcaption>

</figure>

#### 3\. **Pengurangan Jumlah Plugin**:

Saya mengurangi jumlah plugin dari 43 menjadi 19, hanya menyisakan plugin yang benar-benar diperlukan. Ini membantu mengurangi beban server dan mempercepat waktu muat halaman.

#### 4\. **Advanced Speed Optimization**:

Untuk lebih meningkatkan kecepatan, saya melakukan beberapa optimasi lanjutan:

Mengurangi CSS dan JS yang Tidak Digunakan: Menggunakan plugin seperti Asset Manager dan Debloat untuk mengidentifikasi dan menghapus CSS serta JavaScript yang tidak digunakan.

Cache di Level Server: Menggunakan FastCGI cache untuk menyimpan salinan halaman dinamis, sehingga mengurangi beban pada server dan mempercepat waktu muat.

Optimasi PHP-FPM: Menyesuaikan konfigurasi PHP-FPM untuk meningkatkan kinerja pemrosesan PHP.

Mengganti plugin slider dari Revolution Slider ke pilihan yang lebih ringan dan lebih cepat, yaitu Smart Slider 3.

#### 5\. Migrasi Hosting ke VPS dengan LEMP Stack

Saya memindahkan hosting website dari shared hosting ke VPS dengan konfigurasi LEMP stack (Linux, Nginx, MySQL, PHP). Migrasi ini memberikan performa yang jauh lebih baik dan kontrol lebih besar atas konfigurasi server. Selain itu, saya juga membuat script-script bash untuk backup dan restore otomatis, serta menyediakan website staging dan development.

Sistem backup nya seperti ini:

- Backup harian dengan retensi 7 hari
- Backup mingguan dengan retensi 4 minggu
- dan backup bulanan dengan retensi 2 bulan

![Server Backup Configuration](../../assets/images/CleanShot-2024-06-11-at-13.48.44.png "Konfigurasi Backup Otomatis di Server VPS")

Ketika melakukan restore pun, klien dapat memilih file backup yang tersedia (daily, weekly, monthly dan juga pilihan backup manual), dengan 1x perintah saja di terminal dan proses restore berjalan sangat cepat, kurang dari 5 menit.

#### 5\. Security Hardening

Instalasi Ninja Firewall: Saya menginstal plugin Ninja Firewall untuk memberikan perlindungan terhadap ancaman keamanan dan mengkonfigurasinya dengan set up yang cocok dengan konfigurasi server VPS (LEMP)

Keamanan Tambahan di Level Server: Saya juga menerapkan langkah-langkah keamanan tambahan di level server, seperti hardening di Nginx, MariaDB, PHP hingga pengaturan firewall yang lebih ketat.

## Hasil dan Dampak: Peningkatan Score PageSpeed Insights Hingga 240.74%.

### Peningkatan Performa

Peningkatan performa website sangat signifikan, dengan detail sebagai berikut:

| Kriteria | Sebelum Optimasi | Setelah Optimasi | Peningkatan (%) |
|----------|------------------|------------------|-----------------|
| Skor PageSpeed versi mobile | 27 | 92 | 240.74% |
| Skor PageSpeed versi desktop | 36 | 99 | 177.78% |

#### Lampiran Screenshoot hasil PageSpeed After

![After Win Equipment PageSpeed Score](../../assets/images/After-optimasi-Win-Equipment.png "Skor PageSpeed Win Equipment Setelah Optimasi")

### Dampak Positif

Pengurangan Waktu Muat Halaman: Waktu muat halaman yang lebih cepat memberikan pengalaman pengguna yang lebih baik dan mengurangi bounce rate.

Peningkatan Keamanan: Website menjadi lebih aman dari ancaman eksternal dengan adanya langkah-langkah hardening.

Efisiensi Operasional: Pengurangan jumlah plugin dan optimasi server memberikan efisiensi operasional yang lebih baik, mengurangi beban server, dan meningkatkan stabilitas website.

Saya juga melakukan optimasi tambahan untuk meningkatkan score metrik lainnya, yaitu Accessbility, Best Practices dan SEO.

## Siap Meningkatkan Performa Website Anda?

Jika website Anda mengalami kinerja lambat, waktu muat yang lama, atau masalah teknis SEO, kami siap membantu.

Temukan bagaimana strategi optimasi kami dapat secara signifikan mempercepat website Anda, meningkatkan pengalaman pengguna, dan memperkuat peringkat SEO website Anda.

Pelajari lebih lanjut tentang [Layanan Optimasi Kecepatan WordPress kami disini.](/jasa/konversi-website-ke-blocks/)
