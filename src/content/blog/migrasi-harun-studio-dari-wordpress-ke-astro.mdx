---
draft: false
title: "<PERSON><PERSON> Kasus: Migrasi Harun Studio dari WordPress ke Astro"
description: "Pembelajaran dan hasil dari proses migrasi website Harun Studio dari WordPress ke Astro.js dengan peningkatan performa dan pengurangan biaya maintenance."
snippet: "Bagaimana kami meningkatkan kecepatan loading website 8x lipat, mengurangi biaya hosting 100%, dan menyeder<PERSON>kan workflow dengan memigrasikan dari WordPress ke Astro.js."
image: {
    src: "../../assets/blog/2025/April/Mei/harunstudio-after-astro.png",
    alt: "Harun Studio setelah migrasi ke Astro.js"
}
publishDate: "2025-05-01"
category: "STUDI KASUS"
author: "Willya Randika"
tags: ["astro.js", "migrasi", "wordpress", "performa", "jamstack", "cloudflare", "static site"]
---

Saya masih ingat beberapa tahun lalu, ketika memulai <PERSON>run <PERSON>, WordPress menjadi pilihan yang paling masuk akal. Saya sudah menggunakan WordPress sejak 2015 dan saya tidak pernah berpikir sebelumnya untuk mempelajari stack lain seperti Astro. 

Namun, keberadaan AI telah mengubah semuanya. Dunia teknologi web terus berubah. Dengan kemajuan pesat dalam AI dan static site generators, saya mulai mempertanyakan: Apakah WordPress masih menjadi pilihan terbaik untuk website ini?

Setelah evaluasi mendalam, saya memutuskan untuk bermigrasi dari WordPress ke Astro.js dan meng-host website di Cloudflare Pages. Hasilnya? Peningkatan performa yang signifikan, alur kerja yang lebih efisien, dan pengurangan biaya.

Mari kita bahas journey ini lebih detail.

## Situasi Awal: Website WordPress Harun Studio

Sebelum migrasi, website Harun Studio sudah berfungsi dengan baik. Sejujurnya, saya tidak mengalami masalah besar dengan WordPress. Bahkan, saya berhasil mencapai skor 100 di semua metrik PageSpeed Insights—performa, aksesibilitas, praktik terbaik, dan SEO.

Berikut adalah tech stack yang saya gunakan pada WordPress:

* **Theme**: GeneratePress Premium
* **Page Builder**: GenerateBlocks Pro
* **SEO**: Slim SEO
* **Formulir**: Contact Form 7
* **Email**: Bit SMTP
* **Keamanan**: NinjaFirewall
* **Performa**: Nginx-Cache, Flying Pages
* **Utilitas**: Debloat, Fluent Snippet, Flamingo

Meski performa website sudah baik, saya melihat beberapa area yang bisa ditingkatkan:

1. **Maintenance yang Kompleks**: Perlu memperbarui tema, plugin, dan WordPress core secara rutin.
2. **Biaya Berlangganan**: Biaya untuk tema premium (GeneratePress) dan plugin (GenerateBlocks) yang mencapai $149/tahun.
3. **Hosting**: Kebutuhan VPS untuk mendapatkan performa optimal, yang berarti biaya bulanan Rp 100.000.
4. **Workflow**: Meskipun WordPress user-friendly, proses pembaruan konten bisa lebih efisien dengan pendekatan modern.

## Mengapa Memilih Astro.js?

Saat mulai mempertimbangkan migrasi, beberapa framework modern masuk dalam radar saya, termasuk Next.js. Namun, Astro.js unggul karena beberapa alasan:

1. **Fokus pada Content-First**: Astro didesain khusus untuk website yang fokus pada konten, seperti Harun Studio.
2. **Partial Hydration**: Astro hanya mengirimkan JavaScript yang diperlukan, bukan seluruh bundle.
3. **Multi-Framework Support**: Fleksibilitas untuk menggunakan React, Vue, Svelte, atau kerangka kerja lain sesuai kebutuhan.
4. **Performa Luar Biasa**: Default static site generation yang menghasilkan HTML murni yang sangat cepat.
5. **File-Based Routing**: Sistem routing yang intuitif dan mudah dipahami.
6. **Markdown/MDX Support**: Mendukung penulisan konten dengan Markdown dan MDX secara native.
7. **Community yang Berkembang**: Komunitas dan ekosistem yang tumbuh pesat.

![Template AstroWind](../../assets/blog/2025/April/Mei/astrowind-template.png)

## Pemilihan Hosting: Mengapa Cloudflare Pages?

Selain Cloudflare Pages, saya juga mempertimbangkan Vercel dan Netlify. Namun, Cloudflare Pages menjadi pilihan karena:

1. **Biaya Lebih Rendah**: Paket gratis Cloudflare Pages sudah sangat memadai untuk kebutuhan saya.
2. **Integrasi DNS & Proxy**: Integrasi seamless dengan layanan DNS dan proxy Cloudflare yang sudah saya gunakan.
3. **Cloudflare Rules**: Memudahkan pengaturan redirect dan transformasi.
4. **Point of Presence (POP) di Indonesia**: Kehadiran server Cloudflare di Indonesia menjamin kecepatan loading yang lebih baik untuk pengunjung lokal.
5. **Kompatibilitas**: Vercel dan Netlify tidak merekomendasikan penggunaan proxy Cloudflare, sementara saya ingin tetap menggunakan layanan Cloudflare lainnya.
6. **Uptime yang Andal**: Tidak perlu diragukan lagi, uptime Cloudflare sangat bisa diandalkan, memberikan keamanan bahwa website akan selalu tersedia bagi pengunjung.

## Proses Migrasi

### Perencanaan Struktur Konten

Pertama, saya memetakan ulang struktur website untuk memastikan pengalaman pengguna yang lebih baik:

* **Home**: Tetap menjadi halaman utama
* **Pages**: Halaman tentang, kontak, syarat dan ketentuan
* **Blog**: Semua artikel blog ditempatkan di folder `/blog/`
* **Jasa**: Semua halaman layanan menggunakan prefix `/jasa/`

Perubahan URL dilakukan dengan pertimbangan SEO. Misalnya, URL seperti `https://harunstudio.com/jasa-perbaikan-website` diubah menjadi `https://harunstudio.com/jasa/perbaikan-website`. Untuk memastikan tidak ada link yang rusak, saya menggunakan Cloudflare Rules untuk mengatur redirect.

![Cloudflare Rules](../../assets/blog/2025/April/Mei/cloudflare-rules-harunstudio.png)

### Pemilihan Framework dan Tools

Untuk mempercepat proses migrasi, saya memutuskan untuk menggunakan:

1. **Tema Astro**: Saya memilih [Astroship](https://github.com/surjithctly/astroship) sebagai starting point, meskipun belakangan saya merekomendasikan [AstroWind](https://github.com/onwidget/astrowind) karena fiturnya yang lebih lengkap. AstroWind menyediakan sistem tag, kategori, toggle tema dark/light, dan RSS secara default sehingga tidak perlu dikembangkan dari awal lagi.
2. **Export WordPress**: Untuk memigrasikan konten, saya menggunakan [WordPress Export to Markdown](https://github.com/lonekorean/wordpress-export-to-markdown) untuk mengkonversi semua konten WordPress menjadi format Markdown.
3. **Form Handling**: [Web3Forms](https://web3forms.com/) menjadi pilihan untuk menangani formulir kontak dengan kuota 250 submission per bulan yang lebih dari cukup.
4. **SEO Integration**: [Astro SEO](https://github.com/jonasmerlin/astro-seo) membantu mengoptimalkan SEO website dengan fitur-fitur seperti Open Graph, Twitter Cards, dan meta tags yang lengkap.
5. **AI Assistance**: <a href="https://www.cursor.com/referral?code=7DWKJI137053661" rel="nofollow">Cursor AI</a> (referral link) dengan Claude Sonnet 3.7 sangat membantu dalam proses pengembangan. Anda akan mendapatkan 1 bulan gratis ketika sign up paket Pro mereka.

### Redesain dan Pengembangan

Konsep desain keseluruhan tetap sama dengan website WordPress, namun dengan bantuan Cursor AI dan Claude 3.7, saya melakukan redesign beberapa section untuk semua halaman. Proses ini ternyata jauh lebih cepat dari yang saya bayangkan—AI dapat menghasilkan lebih dari 90% kode yang saya butuhkan.

Proses development pun menjadi lebih mudah, karena untuk versi staging saya mengerjakan di localhost. Setiap perubahan ditambahkan dan di-commit ke repository GitHub, kemudian Cloudflare Pages secara otomatis menangkap perubahan tersebut dan melakukan rebuild. Workflow ini sangat meminimalisir error karena saya bisa menguji semua perubahan secara lokal sebelum di-deploy ke production.

Salah satu bukti nyata dari efisiensi AI dalam pengembangan adalah studi kasus yang Anda baca saat ini. 100% ditulis oleh AI dengan Claude Sonnet 3.7, saya hanya memberikan informasi-informasi relevan yang dibutuhkan dalam penulisan studi kasus ini.

![Harun Studio Homepage Setelah Migrasi ke Astro](../../assets/blog/2025/April/Mei/harunstudio-homepage-after-astro.png)

Beberapa tantangan yang saya hadapi selama proses pengembangan:

1. **Mempelajari Syntax Astro**: Meskipun mirip dengan HTML, ada beberapa keunikan yang perlu dipelajari.
2. **Component-Based Thinking**: Mengubah pola pikir dari WordPress ke pendekatan berbasis komponen.
3. **Migration of Complex Forms**: Mengadaptasi formulir kompleks ke Web3Forms.

Untuk membantu Cursor AI dalam menghasilkan kode yang lebih akurat, saya menyertakan dokumentasi Astro dan Tailwind CSS dalam prompt, yang sangat meningkatkan efisiensi proses pengembangan.

## Hasil dan Analisis

### Peningkatan Performa yang Signifikan

Perbedaan performa sebelum dan sesudah migrasi sangat mencolok, mari kita ambil contoh halaman depan Harun Studio:

**Sebelum (WordPress):**
* 37 requests
* 2,15 MB / 1,59 MB transferred
* Finish: 4,96 s

**Sesudah (Astro):**
* 17 requests
* 527,91 kB / 255,22 kB transferred
* Finish: 0,626 s

Itu berarti:
* **54% pengurangan** jumlah HTTP requests
* **75% pengurangan** ukuran transfer
* **8x lebih cepat** waktu loading halaman

![Hasil Diagnostic PageSpeed Harun Studio](../../assets/blog/2025/April/Mei/diagnostic-pagespeed-harunstudio.png)

### Efisiensi Biaya dan Maintenance

1. **Pengurangan Biaya Hosting**: Dari Rp 100.000/bulan untuk VPS menjadi Rp 0 dengan Cloudflare Pages (paket gratis).
2. **Tidak Ada Biaya Langganan**: Tidak perlu lagi berlangganan tema premium dan plugin yang biasanya menghabiskan $149/tahun.
3. **Maintenance yang Lebih Sederhana**: Tidak perlu lagi update WordPress core, tema, dan plugin secara rutin.
4. **Keamanan yang Ditingkatkan**: Website statis secara inheren lebih aman karena tidak ada database atau backend yang bisa diserang.

### Tabel Perbandingan

| Metrik | WordPress | Astro | Perubahan |
|--------|-----------|-------|-----------|
| HTTP Requests | 37 | 17 | -54% |
| Transfer Size | 2,15 MB | 527,91 kB | -75% |
| Loading Time | 4,96 s | 0,626 s | 8x lebih cepat |
| Maintenance | Kompleks | Minimal | Lebih sederhana |
| Biaya Hosting | Rp 100.000/bulan | Rp 0 | -100% |
| Biaya Plugins/Tema | $149/tahun | Rp 0 | -100% |

### Produktivitas yang Meningkat

Dengan bantuan AI seperti Cursor dan Claude 3.7, proses development dan pembaruan konten menjadi jauh lebih efisien:

1. **Penulisan Blog**: AI dapat membantu dalam penulisan dan formatting konten.
2. **Design & Development**: Perubahan UI/UX dapat diimplementasikan dengan bantuan AI.
3. **Debugging**: AI membantu mengidentifikasi dan memperbaiki masalah dengan cepat.
4. **Deployment yang Mulus**: Workflow GitHub + Cloudflare Pages membuat proses deployment menjadi sangat lancar dan terkontrol.

Workflow menjadi lebih efisien karena AI seperti Cursor + Claude Sonnet 3.7 sudah sangat bisa diandalkan. Bahkan, mayoritas kode dalam migrasi ini dihasilkan dengan bantuan AI, yang mempercepat proses development secara signifikan.

## Tantangan dan Lesson Learned

Meskipun migrasi berjalan lancar, ada beberapa pelajaran yang saya dapatkan:

1. **Pilihan Template**: Jika saya harus mengulang, saya akan langsung menggunakan [AstroWind](https://github.com/onwidget/astrowind) karena fiturnya yang lebih lengkap, termasuk sistem tag, kategori, toggle tema dark/light, dan RSS.
2. **Perencanaan Redirect**: Perencanaan yang lebih matang untuk redirect akan menghemat waktu.
3. **Content Management**: Mengadopsi pendekatan yang lebih terstruktur untuk manajemen konten dari awal proyek.

## Astro vs WordPress: Kapan Harus Memilih Mana?

Sejujurnya, untuk kemudahan dalam pengelolaan saya tidak merekomendasikan Astro untuk semua orang. Tidak semua orang paham coding atau minimal paham menggunakan AI tool seperti Cursor dan Windsurf.

WordPress masih menjadi pilihan yang lebih baik jika:
- Anda ingin kemudahan dalam pengelolaan termasuk editing dan penambahan konten
- Tim Anda tidak memiliki background teknis untuk mengelola situs berbasis kode
- Anda memerlukan perubahan konten yang sangat sering dan ingin dilakukan sendiri

Namun, Astro adalah pilihan terbaik saat ini jika:
- Anda serius dalam bisnis dan tidak punya waktu dalam pengembangan website
- Anda biasa menyerahkan tugas pengembangan kepada developer
- Anda menginginkan biaya maintenance yang rendah
- Anda ingin menghemat biaya hosting karena serverless hosting modern seperti Vercel dan Cloudflare Pages sudah jauh lebih cukup (mereka memberikan 100GB bandwidth dalam paket gratis, yang sangat banyak untuk website statis)

Perlu dicatat bahwa dengan WordPress pun Anda tetap bisa mendapatkan performa 100 dan lolos Core Web Vitals, asalkan stack yang Anda gunakan ringan, misalnya GeneratePress dan GenerateBlocks.

## Rencana ke Depan

Setelah berhasil melakukan migrasi, beberapa rencana untuk website Harun Studio ke depan:

1. **Pengembangan Blog**: Memperluas konten blog dengan studi kasus dan tutorial.
2. **i18n Support**: Menambahkan dukungan multi-bahasa.
3. **Web Vitals Monitoring**: Implementasi monitoring berkelanjutan untuk Core Web Vitals.
4. **Progressive Web App (PWA)**: Mengeksplorasi kemungkinan untuk membuat Harun Studio menjadi PWA.

## Kesimpulan

Migrasi dari WordPress ke Astro.js terbukti menjadi langkah yang tepat untuk Harun Studio. Dengan peningkatan performa yang signifikan, pengurangan biaya, dan alur kerja yang lebih efisien, website kini lebih siap untuk mendukung pertumbuhan bisnis di masa depan.

Bagi Anda yang sedang mempertimbangkan migrasi serupa, saya sangat merekomendasikan untuk mempertimbangkan Astro.js, terutama jika website Anda berfokus pada konten. Dengan bantuan AI modern seperti Cursor dan Claude, proses migrasi dan pengembangan dapat menjadi jauh lebih cepat dan efisien.

Harun Studio juga menyediakan [layanan migrasi dari WordPress ke Astro](/jasa/migrasi-wordpress-ke-astro) bagi Anda yang ingin mendapatkan manfaat dari teknologi modern ini tanpa harus melalui kurva pembelajaran yang curam.

## Resources dan Tools yang Digunakan

* [Astro Documentation](https://docs.astro.build/en/getting-started/)
* [AstroWind Template](https://github.com/onwidget/astrowind)
* [WordPress Export to Markdown](https://github.com/lonekorean/wordpress-export-to-markdown)
* [Web3Forms Documentation](https://docs.web3forms.com/)
* [Cursor AI](https://www.cursor.com/referral?code=7DWKJI137053661) (referral link)
* [Patrick Thurmond's Migration Guide](https://www.patrickthurmond.com/blog/2023/10/15/how-i-migrated-from-wordpress-to-astro-js)

Apakah Anda tertarik untuk memigrasikan website Anda dari WordPress ke solusi modern seperti Astro? Atau mungkin Anda memiliki pertanyaan tentang proses migrasi? Jangan ragu untuk [menghubungi kami](/hubungi-kami) untuk diskusi lebih lanjut. 