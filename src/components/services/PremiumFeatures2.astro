---
import { Icon } from "astro-icon/components";

interface PremiumFeature {
  title: string;
  description: string;
  icon: string;
  bullets?: string[];
}

interface Props {
  title: string;
  description: string;
  features: PremiumFeature[];
}

const { title, description, features } = Astro.props;
---

<section class="py-20 relative overflow-hidden">
  <!-- Background elements -->
  <div class="absolute inset-0 -z-10 bg-gradient-to-br from-gray-50 to-white"></div>
  <div class="absolute top-0 left-1/2 -translate-x-1/2 w-[800px] h-[800px] bg-[var(--color-brand-50)] rounded-full opacity-30 blur-3xl -z-10"></div>
  <div class="absolute -bottom-96 -right-96 w-[800px] h-[800px] bg-yellow-50 rounded-full opacity-30 blur-3xl -z-10"></div>
  
  <div class="max-w-7xl mx-auto px-4">
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[var(--color-brand-50)] mb-4">
        <Icon name="lucide:sparkles" class="w-4 h-4 text-[var(--color-brand)]" />
        <span class="text-sm font-medium text-[var(--color-brand)]">Premium Features</span>
      </div>
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-gray-900)">{title}</h2>
      <p class="text-lg" style="color: var(--color-gray-600)">{description}</p>
    </div>
    
    <!-- Premium Features -->
    <div class="flex flex-col gap-16">
      {features.map((feature, index) => (
        <div class={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-8 lg:gap-16 items-center`}>
          <!-- Visual side -->
          <div class="lg:w-5/12">
            <div class={`relative p-1 rounded-2xl bg-gradient-to-br ${
              index === 0 ? 'from-blue-500 to-purple-500' : 
              index === 1 ? 'from-[var(--color-brand)] to-yellow-500' : 
              'from-amber-500 to-red-500'
            }`}>
              <div class="bg-white rounded-xl p-8 md:p-10">
                <div class="relative">
                  <!-- Decorative elements -->
                  <div class="absolute -inset-4 bg-gray-50 rounded-lg -z-10"></div>
                  <div class={`absolute -inset-1 bg-gradient-to-br opacity-10 rounded-lg -z-10 ${
                    index === 0 ? 'from-blue-500 to-purple-500' : 
                    index === 1 ? 'from-[var(--color-brand)] to-yellow-500' : 
                    'from-amber-500 to-red-500'
                  }`}></div>
                  
                  <!-- Icon -->
                  <div class="flex justify-center">
                    <div class={`w-24 h-24 rounded-2xl bg-gradient-to-br ${
                      index === 0 ? 'from-blue-500 to-purple-500' : 
                      index === 1 ? 'from-[var(--color-brand)] to-yellow-500' : 
                      'from-amber-500 to-red-500'
                    } flex items-center justify-center shadow-lg`}>
                      <Icon name={feature.icon} class="w-12 h-12 text-white" />
                    </div>
                  </div>
                  
                  <!-- Feature name -->
                  <div class="text-center mt-8">
                    <h3 class="text-2xl md:text-3xl font-bold" style="color: var(--color-gray-900)">{feature.title}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Content side -->
          <div class="lg:w-7/12">
            <p class="text-lg md:text-xl mb-8" style="color: var(--color-gray-700)">{feature.description}</p>
            
            {feature.bullets && feature.bullets.length > 0 && (
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {feature.bullets.map(bullet => (
                  <div class="flex items-center gap-4 bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                    <div class={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                      index === 0 ? 'bg-blue-100 text-blue-600' : 
                      index === 1 ? 'bg-[var(--color-brand-50)] text-[var(--color-brand)]' : 
                      'bg-amber-100 text-amber-600'
                    }`}>
                      <Icon name="lucide:check" class="w-5 h-5" />
                    </div>
                    <span class="text-base md:text-lg font-medium text-gray-700">{bullet}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  </div>
</section>
