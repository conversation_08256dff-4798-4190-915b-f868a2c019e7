---
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import Link from "@/components/ui/link.astro";

const {
  showCta = false,
  ctaBadge,
  ctaTitle,
  ctaSubtitle,
  ctaPriceText, // Consider splitting this if needed
  ctaButtonLink,
  ctaButtonText,
  ctaFootnote,
  processTitle,
  processSubtitle,
  steps = [] // Default to empty array
} = Astro.props;

// Langkah-langkah proses
// const steps = [...]; // Removed hardcoded data
---

<section class="relative my-4 md:my-8 overflow-hidden">
  <div class="max-w-7xl mx-auto px-4 flex flex-col items-center">
    <!-- Strong CTA Section -->
    {showCta && (
      <div class="mb-20 relative z-10 rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 shadow-xl overflow-hidden w-full">
        <div class="flex flex-col md:flex-row">
          <div class="flex-1 flex flex-col justify-center items-center text-center p-10 md:p-14 relative z-10 overflow-hidden">
            <div class="absolute -right-16 -top-16 w-56 h-56 rounded-full bg-gradient-to-r from-[var(--color-brand)] to-blue-600 opacity-20 blur-3xl"></div>
            {ctaBadge && (
              <div class="inline-block mb-6 px-4 py-1 bg-white/10 backdrop-blur-sm rounded-full">
                <span class="text-white/80 text-sm">{ctaBadge}</span>
              </div>
            )}
            <h3 class="mb-6 text-white text-3xl md:text-4xl font-bold">{ctaTitle}</h3>
            {/* This part might need adjustment if ctaPriceText needs HTML */} 
            <p class="text-xl md:text-2xl mb-8 text-gray-300 max-w-xl mx-auto" set:html={ctaSubtitle?.replace(ctaPriceText, `<span class="font-bold text-white">${ctaPriceText}</span>`)} />
            {ctaButtonLink && ctaButtonText && (
              <div class="flex flex-col sm:flex-row gap-4 justify-center w-full">
                <Link href={ctaButtonLink} class="border border-white/20 hover:bg-white/10 text-white rounded-lg inline-flex items-center justify-center px-8 py-3 transition-colors" rel="noopener noreferrer nofollow">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" class="mr-2" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.0025 0H9.9975C4.48375 0 0 4.485 0 10C0 12.1875 0.705 14.215 1.90375 15.8612L0.6575 19.5763L4.50125 18.3475C6.0825 19.395 7.96875 20 10.0025 20C15.5162 20 20 15.5138 20 10C20 4.48625 15.5162 0 10.0025 0Z" fill="#4CAF50"/>
                    <path d="M15.8212 14.1212C15.5799 14.8025 14.6224 15.3675 13.8587 15.5325C13.3362 15.6437 12.6537 15.7325 10.3562 14.78C7.41744 13.5625 5.52494 10.5763 5.37744 10.3825C5.23619 10.1887 4.18994 8.80123 4.18994 7.36623C4.18994 5.93123 4.91869 5.23248 5.21244 4.93248C5.45369 4.68623 5.85244 4.57373 6.23494 4.57373C6.35869 4.57373 6.46994 4.57998 6.56994 4.58498C6.86369 4.59748 7.01119 4.61498 7.20494 5.07873C7.44619 5.65998 8.03369 7.09498 8.10369 7.24248C8.17494 7.38998 8.24619 7.58998 8.14619 7.78373C8.05244 7.98373 7.96994 8.07248 7.82244 8.24248C7.67494 8.41248 7.53494 8.54248 7.38744 8.72498C7.25244 8.88373 7.09994 9.05373 7.26994 9.34748C7.43994 9.63498 8.02744 10.5937 8.89244 11.3637C10.0087 12.3575 10.9137 12.675 11.2374 12.81C11.4787 12.91 11.7662 12.8862 11.9424 12.6987C12.1662 12.4575 12.4424 12.0575 12.7237 11.6637C12.9237 11.3812 13.1762 11.3462 13.4412 11.4462C13.7112 11.54 15.1399 12.2462 15.4337 12.3925C15.7274 12.54 15.9212 12.61 15.9924 12.7337C16.0624 12.8575 16.0624 13.4387 15.8212 14.1212Z" fill="#FAFAFA"/>
                  </svg>
                  {ctaButtonText}
                </Link>
              </div>
            )}
            {ctaFootnote && <p class="text-sm mt-6 text-white/80">{ctaFootnote}</p>}
          </div>
        </div>
      </div>
    )}

    {/* Process Section dengan background full width yang benar-benar melebar ke seluruh layar */}
    <section class="relative w-full -mt-32 pt-24 pb-20 px-2 md:px-8 overflow-visible">
      <div class="absolute left-1/2 top-0 -translate-x-1/2 w-screen h-full bg-[var(--color-brand-50)] z-0"></div>
      <div class="relative z-10 max-w-7xl mx-auto">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <h2 class="mb-4" style="color: var(--color-gray-900)">{processTitle}</h2>
          <p class="text-lg" style="color: var(--color-gray-600)">
            {processSubtitle}
          </p>
        </div>
        {/* Modern Interactive Accordion Process Steps */}
        {steps.length > 0 && (
          <div class="w-full max-w-7xl mx-auto">
            <div class="process-accordion space-y-6">
              {steps.map((step, index) => (
                <div class="process-step bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                  {/* Step Header (always visible) */}
                  <div 
                    class="process-header flex items-center p-6 cursor-pointer hover:bg-gray-50 transition-colors"
                    data-step={index}
                  >
                    <div class="process-number mr-6 flex-shrink-0">
                      <div class="w-16 h-16 rounded-full bg-[var(--color-brand-50)] flex items-center justify-center relative">
                        <span class="text-3xl font-bold text-[var(--color-brand)]">{step.number}</span>
                        {step.icon && (
                          <div class="absolute -right-1 -bottom-1 w-8 h-8 rounded-full bg-white shadow flex items-center justify-center border-2 border-[var(--color-brand-50)]">
                            <Icon name={step.icon} class="w-4 h-4 text-[var(--color-brand)]" />
                          </div>
                        )}
                      </div>
                    </div>
                    <div class="process-title-area flex-grow pr-4">
                      <h3 class="text-xl font-bold" style="color: var(--color-gray-900)">{step.title}</h3>
                      <p class="text-gray-500 line-clamp-1">{step.description}</p>
                    </div>
                    <div class="accordion-arrow flex-shrink-0 w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center transform transition-transform">
                      <Icon name="lucide:chevron-down" class="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                  {/* Step Content (expands when header is clicked) */}
                  <div class="process-content overflow-hidden max-h-0 transition-all duration-300 ease-in-out">
                    <div class="p-6 pt-4 border-t border-gray-100">
                      <div class="grid md:grid-cols-5 gap-6">
                        <div class="md:col-span-3">
                          <p class="mb-4 text-gray-600">{step.description}</p>
                          {step.details && (
                            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                              <p class="text-gray-700">{step.details}</p>
                            </div>
                          )}
                        </div>
                        <div class="md:col-span-2">
                          <div class="bg-[var(--color-brand-50)] rounded-lg p-5 flex flex-col items-center justify-center h-full">
                            <div class="w-16 h-16 rounded-full bg-white shadow-md flex items-center justify-center mb-4">
                              <Icon name={step.icon} class="w-8 h-8 text-[var(--color-brand)]" />
                            </div>
                            {step.detailTags && step.detailTags.length > 0 && (
                              <div class="flex flex-wrap justify-center gap-2 mt-2">
                                {step.detailTags.map(tag => (
                                  <div class="flex items-center gap-1 bg-white px-3 py-1.5 rounded-full text-[var(--color-brand)] text-xs shadow-sm">
                                    {tag.icon && <Icon name={tag.icon} class="w-3 h-3" />}
                                    <span>{tag.text}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  </div>
</section>

<script>
  // Accordion functionality (Safari-friendly)
  document.addEventListener('DOMContentLoaded', function () {
    const headers = document.querySelectorAll('.process-header');
    const contents = document.querySelectorAll('.process-content');
    const arrows = document.querySelectorAll('.accordion-arrow');

    // Function to open an accordion item
    function openAccordion(content, arrow) {
      if (!content || !arrow) return;
      (content as HTMLElement).style.maxHeight = (content as HTMLElement).scrollHeight + 'px';
      arrow.classList.add('rotate-180');
    }

    // Function to close an accordion item
    function closeAccordion(content, arrow) {
      if (!content || !arrow) return;
      (content as HTMLElement).style.maxHeight = '';
      arrow.classList.remove('rotate-180');
    }

    // Open the first item by default
    if (contents.length > 0 && arrows.length > 0) {
      openAccordion(contents[0], arrows[0]);
    }

    // Add touch and click event listeners
    headers.forEach(function (header, i) {
      const content = header.nextElementSibling;
      const arrow = header.querySelector('.accordion-arrow');
      
      if (!content || !arrow) return;

      // Add both touch and click events for better mobile support
      ['touchstart', 'click'].forEach(eventType => {
        header.addEventListener(eventType, function(e) {
          // Prevent default only for touch events to avoid double triggering
          if (eventType === 'touchstart') {
            e.preventDefault();
          }

          // Close all other accordions
          contents.forEach((c, idx) => {
            if (c !== content) {
              closeAccordion(c, arrows[idx]);
            }
          });

          // Toggle the clicked item
          if ((content as HTMLElement).style.maxHeight) {
            closeAccordion(content, arrow);
          } else {
            openAccordion(content, arrow);
          }
        });
      });
    });
  });
</script>

<style>
  .process-step {
    transition: all 0.3s ease;
  }
  
  .process-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
  }

  /* Add touch-friendly styles */
  .process-header {
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;
    user-select: none;
  }

  .process-content {
    transition: max-height 0.3s ease-in-out;
    overflow: hidden;
  }

  .accordion-arrow {
    transition: transform 0.3s ease;
  }

  .accordion-arrow.rotate-180 {
    transform: rotate(180deg);
  }
</style> 