---
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import { Image } from 'astro:assets'; // Import Image for potential Astro Assets usage

const {
  title,
  subtitle,
  listItems = [], // Default to empty array
  buttonText,
  buttonLink,
  imageSrc, // Expects an image object or string path
  imageAlt,
  imageObjectFit = 'contain', // New prop: 'contain' or 'cover'
  showImageShadow = true, // New prop: boolean
  id
} = Astro.props;

// Construct dynamic classes for desktop image
const desktopImageBaseClasses = "h-full w-full object-center min-h-[400px] rounded-xl";
const desktopImageFitClass = imageObjectFit === 'cover' ? 'object-cover' : 'object-contain';
const desktopImageShadowClass = showImageShadow ? 'shadow-[0_0_40px_rgba(0,0,0,0.1),0_0_20px_rgba(0,0,0,0.08)]' : '';
const desktopImageClasses = `${desktopImageBaseClasses} ${desktopImageFitClass} ${desktopImageShadowClass}`.trim();

// Mobile image classes (shadow is always applied here, fit is always contain)
const mobileImageClasses = "w-full rounded-xl shadow-lg object-contain";
---

<section id={id ?? undefined} class="py-10 md:py-20 relative overflow-visible">

  <div class="max-w-7xl mx-auto px-4">
    <div class="flex flex-col lg:flex-row items-center">
      <!-- Mobile image (show first on mobile, overlap upward) -->
      <div class="w-full lg:hidden -mt-24 px-2">
        <div>
          {/* Use Astro Image component or standard img tag based on imageSrc type */} 
          {typeof imageSrc === 'object' ? (
            <Image 
              src={imageSrc} 
              alt={imageAlt} 
              class={mobileImageClasses}
              loading="lazy"
            />
          ) : (
            <img 
              src={imageSrc} 
              alt={imageAlt} 
              class={mobileImageClasses}
              loading="lazy"
            />
          )}
        </div>
      </div>
      <!-- Left side - Content -->
      <div class="lg:w-[40%] lg:pr-16 z-10 py-8">
        <div class="bg-transparent p-2 lg:p-0">
          <h2 class="mb-6" style="color: var(--color-gray-900)">{title}</h2>
          
          <p class="mb-8 text-lg" style="color: var(--color-gray-700)">
            {subtitle}
          </p>
          
          {listItems.length > 0 && (
            <div class="space-y-3 mb-8">
              {listItems.map((item) => (
                <div class="flex items-center gap-2">
                  <div class="w-7 h-7 rounded-full bg-white flex items-center justify-center flex-shrink-0">
                    <Icon name="lucide:check" class="w-5 h-5 text-[var(--color-brand)]" />
                  </div>
                  <p style="color: var(--color-gray-900)">{item}</p>
                </div>
              ))}
            </div>
          )}
          
          {buttonText && buttonLink && (
            <a href={buttonLink}>
              <Button size="lg" style="primary" class="w-full sm:w-auto px-8 py-3 flex gap-2 items-center justify-center">
                {buttonText}
              </Button>
            </a>
          )}
        </div>
      </div>
      
      <!-- Right side - Image (desktop only) -->
      <div class="lg:absolute lg:right-0 lg:top-[-4rem] lg:w-[51%] hidden lg:block">
        <div class="relative p-3 h-full">
          {/* Use Astro Image component or standard img tag based on imageSrc type */} 
          {typeof imageSrc === 'object' ? (
            <Image 
              src={imageSrc} 
              alt={imageAlt} 
              class={desktopImageClasses}
            />
           ) : (
             <img 
              src={imageSrc} 
              alt={imageAlt} 
              class={desktopImageClasses}
            />
           )}
        </div>
      </div>
    </div>
  </div>
</section> 