---
import { Icon } from "astro-icon/components";

interface PremiumFeature {
  title: string;
  description: string;
  icon: string;
  bullets?: string[];
}

interface Props {
  title: string;
  description: string;
  features: PremiumFeature[];
}

const { title, description, features } = Astro.props;
---

<section class="py-16 relative">
  <!-- Decorative background elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
    <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
  </div>
  
  <div class="max-w-7xl mx-auto px-4">
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-gray-900)">{title}</h2>
      <p class="text-lg" style="color: var(--color-gray-600)">{description}</p>
    </div>
    
    <!-- Premium Features -->
    <div class="relative">
      <!-- Background accent -->
      <div class="absolute -inset-4 bg-gradient-to-br from-yellow-50 to-[var(--color-brand-50)] rounded-3xl -z-10"></div>
      
      <!-- Content container -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <!-- Premium badge -->
        <div class="bg-gradient-to-r from-[var(--color-brand)] to-yellow-400 py-3 px-6 flex justify-between items-center">
          <div class="flex items-center gap-2">
            <Icon name="lucide:star" class="w-5 h-5 text-white" />
            <span class="text-white font-bold tracking-wide">PREMIUM FEATURES</span>
          </div>
          <div class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
            <span class="text-white text-sm font-medium">Exclusive Benefits</span>
          </div>
        </div>
        
        <!-- Features -->
        <div class="divide-y divide-gray-100">
          {features.map((feature, index) => (
            <div class="p-8 md:p-10 flex flex-col md:flex-row gap-8 items-start">
              <!-- Icon column -->
              <div class="md:w-1/5 flex-shrink-0">
                <div class={`w-16 h-16 rounded-xl bg-gradient-to-br ${
                  index === 0 ? 'from-blue-50 to-blue-100 text-blue-600' : 
                  index === 1 ? 'from-purple-50 to-purple-100 text-purple-600' : 
                  'from-amber-50 to-amber-100 text-amber-600'
                } flex items-center justify-center`}>
                  <Icon name={feature.icon} class="w-8 h-8" />
                </div>
              </div>
              
              <!-- Content column -->
              <div class="md:w-4/5">
                <h3 class="text-xl md:text-2xl font-bold mb-3" style="color: var(--color-gray-900)">{feature.title}</h3>
                <p class="text-base md:text-lg mb-6" style="color: var(--color-gray-700)">{feature.description}</p>
                
                {feature.bullets && feature.bullets.length > 0 && (
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {feature.bullets.map(bullet => (
                      <div class="flex items-center gap-3">
                        <div class="w-6 h-6 rounded-full bg-[var(--color-brand-50)] flex items-center justify-center flex-shrink-0">
                          <Icon name="lucide:check" class="text-[var(--color-brand)] w-4 h-4" />
                        </div>
                        <span class="text-gray-700">{bullet}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
</section>
