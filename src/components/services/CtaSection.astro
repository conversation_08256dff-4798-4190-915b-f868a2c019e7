---
import Link from "@/components/ui/link.astro";
import { Icon } from "astro-icon/components";
import { Image } from 'astro:assets'; // Import Image for potential Astro Assets usage

const {
  urgencyBadgeText, // Menggantikan prop 'badge'
  title,
  subtitle,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink,
  imageSrc, // Expects an image object or string path
  imageAlt,
  showImage = true // Control whether to show the image section
} = Astro.props;
---

<section class="relative py-10 md:py-20 overflow-hidden">
  <div class="max-w-7xl mx-auto px-4">
    <div class="absolute inset-0 -z-10 bg-gradient-to-b from-white to-[var(--color-brand-50)]"></div>

    <!-- Background elements -->
    <div class="absolute inset-0 -z-10">
    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
    <div class="absolute -right-32 -top-32 w-96 h-96 bg-[var(--color-brand-50)] rounded-full opacity-30 blur-3xl"></div>
    <div class="absolute -left-32 bottom-0 w-96 h-96 bg-[var(--color-brand-50)] rounded-full opacity-30 blur-3xl"></div>
  </div>

  <div class="max-w-global mx-auto px-2">
    <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl shadow-xl overflow-hidden">
      <div class="flex flex-col md:flex-row">
        <!-- Left: Content -->
        <div class="flex-1 flex flex-col justify-center items-start text-left p-8 md:p-14 relative z-10 overflow-hidden">
          <!-- Decorative elements -->
          <div class="absolute -right-16 -top-16 w-56 h-56 rounded-full bg-gradient-to-r from-[var(--color-brand)] to-blue-600 opacity-20 blur-3xl"></div>

          {/* Urgency Badge - Tampilkan secara kondisional */}
          {urgencyBadgeText && (
            <div class="urgency-badge mb-6 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-red-50 to-orange-50 px-4 py-2 text-sm font-medium text-red-700 ring-1 ring-red-200 animate-pulse">
              <Icon name="lucide:clock" class="h-4 w-4 text-red-600" />
              <span>{urgencyBadgeText}</span>
            </div>
          )}

          <h2 class="mb-6 text-white" set:html={title}></h2>

          <p class="text-lg mb-8 text-gray-300 max-w-xl">
            {subtitle}
          </p>

          <div class="flex flex-col sm:flex-row gap-4 w-full">
            {primaryButtonText && primaryButtonLink && (
              <Link style="inverted" size="lg" class="font-medium px-8 py-3 bg-white text-gray-900 hover:bg-white/90" href={primaryButtonLink} rel="noopener noreferrer nofollow">
                <span class="flex items-center justify-center">
                  {primaryButtonText}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 ml-2">
                    <path fill-rule="evenodd" d="M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z" clip-rule="evenodd" />
                  </svg>
                </span>
              </Link>
            )}
            {secondaryButtonText && secondaryButtonLink && (
              <Link href={secondaryButtonLink} target="_blank" rel="noopener noreferrer nofollow" class="border border-white/20 hover:bg-white/10 text-white rounded-lg inline-flex items-center justify-center px-8 py-3 transition-colors">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" class="mr-2" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10.0025 0H9.9975C4.48375 0 0 4.485 0 10C0 12.1875 0.705 14.215 1.90375 15.8612L0.6575 19.5763L4.50125 18.3475C6.0825 19.395 7.96875 20 10.0025 20C15.5162 20 20 15.5138 20 10C20 4.48625 15.5162 0 10.0025 0Z" fill="#4CAF50"/>
                  <path d="M15.8212 14.1212C15.5799 14.8025 14.6224 15.3675 13.8587 15.5325C13.3362 15.6437 12.6537 15.7325 10.3562 14.78C7.41744 13.5625 5.52494 10.5763 5.37744 10.3825C5.23619 10.1887 4.18994 8.80123 4.18994 7.36623C4.18994 5.93123 4.91869 5.23248 5.21244 4.93248C5.45369 4.68623 5.85244 4.57373 6.23494 4.57373C6.35869 4.57373 6.46994 4.57998 6.56994 4.58498C6.86369 4.59748 7.01119 4.61498 7.20494 5.07873C7.44619 5.65998 8.03369 7.09498 8.10369 7.24248C8.17494 7.38998 8.24619 7.58998 8.14619 7.78373C8.05244 7.98373 7.96994 8.07248 7.82244 8.24248C7.67494 8.41248 7.53494 8.54248 7.38744 8.72498C7.25244 8.88373 7.09994 9.05373 7.26994 9.34748C7.43994 9.63498 8.02744 10.5937 8.89244 11.3637C10.0087 12.3575 10.9137 12.675 11.2374 12.81C11.4787 12.91 11.7662 12.8862 11.9424 12.6987C12.1662 12.4575 12.4424 12.0575 12.7237 11.6637C12.9237 11.3812 13.1762 11.3462 13.4412 11.4462C13.7112 11.54 15.1399 12.2462 15.4337 12.3925C15.7274 12.54 15.9212 12.61 15.9924 12.7337C16.0624 12.8575 16.0624 13.4387 15.8212 14.1212Z" fill="#FAFAFA"/>
                </svg>
                {secondaryButtonText}
              </Link>
            )}
          </div>
        </div>

        <!-- Right: Image -->
        {showImage && imageSrc && imageAlt && (
          <div class="hidden md:flex md:w-1/2 relative overflow-visible">
            <div class="absolute inset-0 flex items-center justify-center px-8">
              <div class="relative w-full h-full max-w-[120%]">
                {typeof imageSrc === 'object' ? (
                  <Image
                    src={imageSrc}
                    alt={imageAlt}
                    class="w-full h-full object-contain object-center"
                    loading="lazy"
                    width={800}
                    height={600}
                  />
                ) : (
                  <img
                    src={imageSrc}
                    alt={imageAlt}
                    class="w-full h-full object-contain object-center"
                    loading="lazy"
                    style="transform: scale(1.2); transform-origin: center;"
                  />
                )}
              </div>
            </div>
            <!-- Overlay gradient -->
            <div class="absolute inset-0 bg-gradient-to-r from-gray-900/20 via-gray-900/5 to-transparent"></div>
          </div>
        )}
      </div>
    </div>
  </div>
</section>
