---
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import { Image } from 'astro:assets';

const {
  title = "Studi Kasus",
  subtitle = "Lihat bagaimana kami menerapkan solusi untuk klien nyata",
  caseTitle = "Studi Kasus: Migrasi Harun Studio dari WordPress ke Astro",
  caseDescription = "Bagaimana kami meningkatkan kecepatan loading website 8x lipat, mengurangi biaya hosting 100%, dan menyederhanakan workflow.",
  metrics = [], // Array of metrics to display
  imageSrc, // Path to featured image
  imageAlt = "Studi Kasus",
  caseLink = "/blog/migrasi-harun-studio-dari-wordpress-ke-astro", // Link to full case study
  buttonText = "Baca Studi Kasus Lengkap",
  id = "case-study"
} = Astro.props;

// Dynamic classes for image
const imageClasses = "w-full rounded-xl shadow-lg object-cover h-full";
---

<section id={id} class="pb-16">
  <div class="max-w-7xl mx-auto px-4">
    <!-- Section Header -->
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-gray-900)">{title}</h2>
      <p class="max-w-3xl mx-auto text-lg" style="color: var(--color-gray-700)">{subtitle}</p>
    </div>

    <!-- Case Study Card -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div class="flex flex-col lg:flex-row">
        <!-- Case Study Image -->
        <div class="lg:w-1/2 h-64 lg:h-auto">
          {typeof imageSrc === 'object' ? (
            <Image 
              src={imageSrc} 
              alt={imageAlt}
              class={imageClasses}
            />
          ) : (
            <img 
              src={imageSrc} 
              alt={imageAlt}
              class={imageClasses}
            />
          )}
        </div>
        
        <!-- Case Study Content -->
        <div class="lg:w-1/2 p-6 lg:p-10 flex flex-col justify-between">
          <div>
            <div class="inline-block px-4 py-1 mb-4 text-sm font-semibold rounded-full" style="background-color: var(--color-brand-lightest); color: var(--color-brand-dark);">
              STUDI KASUS
            </div>
            
            <h3 class="text-2xl font-bold mb-3" style="color: var(--color-gray-900)">{caseTitle}</h3>
            <p class="mb-6" style="color: var(--color-gray-700)">{caseDescription}</p>
            
            <!-- Metrics Display -->
            {metrics.length > 0 && (
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                {metrics.map((metric) => (
                  <div class="flex items-start gap-3">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: var(--color-brand-lightest);">
                      <Icon name={metric.icon || "lucide:trending-up"} class="w-5 h-5" style="color: var(--color-brand);" />
                    </div>
                    <div>
                      <div class="font-bold text-xl" style="color: var(--color-gray-900)">{metric.value}</div>
                      <div class="text-sm" style="color: var(--color-gray-700)">{metric.label}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div class="mt-4">
            <a href={caseLink}>
              <Button size="lg" style="primary" class="w-full sm:w-auto px-8 py-3 flex gap-2 items-center justify-center">
                {buttonText}
                <Icon name="lucide:arrow-right" class="w-5 h-5" />
              </Button>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
