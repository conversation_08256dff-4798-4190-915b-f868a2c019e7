---
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";

const {
  title,
  subtitle,
  faqs = [], // Default to empty array
  contactText,
  contactButtonText,
  contactButtonLink
} = Astro.props;

// FAQ data
// const faqs = [...]; // Removed hardcoded data
---

<section class="py-10 md:py-10 relative">
  <!-- Background gradient element -->
  <div class="max-w-7xl mx-auto px-4">
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="mb-4" style="color: var(--color-gray-900)">{title}</h2>
      <p class="text-base md:text-lg" style="color: var(--color-gray-600)">
        {subtitle}
      </p>
    </div>

    {faqs.length > 0 && (
      <div class="max-w-3xl mx-auto">
        <div class="space-y-3 md:space-y-4">
          {faqs.map((faq, index) => (
            <div class="border border-gray-200 rounded-xl overflow-hidden hover:shadow-md transition-shadow">
              <details class="group">
                <summary class="flex items-center justify-between gap-3 px-4 md:px-6 py-3 md:py-4 bg-white cursor-pointer">
                  <h3 class="text-base md:text-lg font-medium text-gray-900">{faq.question}</h3>
                  <div class="relative w-5 h-5 transition-transform duration-300 group-open:rotate-180">
                    <Icon name="lucide:chevron-down" class="w-5 h-5 text-[var(--color-brand)]" />
                  </div>
                </summary>
                <div class="px-4 md:px-6 py-3 md:py-4 border-t border-gray-100 bg-gray-50">
                  {/* Use set:html for answers that might contain line breaks */} 
                  <p class="text-sm md:text-base text-gray-700 whitespace-pre-line" set:html={faq.answer}></p>
                </div>
              </details>
            </div>
          ))}
        </div>

        {contactText && contactButtonText && contactButtonLink && (
          <div class="mt-8 md:mt-12 text-center">
            <p class="text-gray-600 mb-4">{contactText}</p>
            <a href={contactButtonLink} target="_blank" rel="noopener noreferrer" class="inline-flex items-center gap-2 text-[var(--color-brand)] font-medium hover:underline px-4 py-2 rounded-lg hover:bg-[var(--color-brand-50)] transition-colors">
              <Icon name="lucide:message-square" class="w-4 h-4 flex-shrink-0" />
              {contactButtonText}
            </a>
          </div>
        )}
      </div>
    )}
  </div>
</section> 