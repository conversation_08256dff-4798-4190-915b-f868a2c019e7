---
import { Icon } from "astro-icon/components";

interface PremiumFeature {
  title: string;
  description: string;
  icon: string;
  bullets?: string[];
}

interface Props {
  title: string;
  description: string;
  features: PremiumFeature[];
}

const { title, description, features } = Astro.props;
---

<section class="py-20 relative overflow-hidden">
  <!-- Background elements -->
  <div class="absolute inset-0 -z-10 bg-gradient-to-b from-gray-50 via-white to-gray-50"></div>
  <div class="absolute top-40 left-0 w-full h-[500px] bg-[var(--color-brand-50)] skew-y-3 -z-10"></div>
  
  <div class="max-w-7xl mx-auto px-4">
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[var(--color-brand)] to-yellow-400 mb-4 shadow-md">
        <Icon name="lucide:crown" class="w-4 h-4 text-white" />
        <span class="text-sm font-bold text-white">PREMIUM</span>
      </div>
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-gray-900)">{title}</h2>
      <p class="text-lg" style="color: var(--color-gray-600)">{description}</p>
    </div>
    
    <!-- Premium Features Showcase -->
    <div class="relative">
      <!-- Tab Navigation -->
      <div class="flex flex-wrap justify-center gap-4 mb-12" id="premium-tabs">
        {features.map((feature, index) => (
          <button 
            class="premium-tab-btn group flex items-center gap-3 px-5 py-3 rounded-full border-2 transition-all duration-300" 
            data-index={index}
            data-active={index === 0 ? "true" : "false"}
          >
            <div class="w-8 h-8 rounded-full flex items-center justify-center">
              <Icon name={feature.icon} class="w-5 h-5" />
            </div>
            <span class="font-medium">{feature.title}</span>
          </button>
        ))}
      </div>
      
      <!-- Content Panels -->
      <div class="relative bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <!-- Premium badge -->
        <div class="absolute top-6 right-6 bg-gradient-to-r from-[var(--color-brand)] to-yellow-400 px-4 py-2 rounded-lg shadow-lg z-10">
          <span class="text-white font-bold tracking-wide text-sm">PREMIUM FEATURE</span>
        </div>
        
        <!-- Feature Panels -->
        {features.map((feature, index) => (
          <div 
            class="premium-tab-panel p-8 md:p-12" 
            data-index={index}
            style={{display: index === 0 ? "block" : "none"}}
          >
            <div class="flex flex-col lg:flex-row gap-12">
              <!-- Visual Column -->
              <div class="lg:w-2/5">
                <div class="bg-gradient-to-br from-gray-50 to-white p-8 rounded-xl border border-gray-100 shadow-inner">
                  <div class="w-20 h-20 rounded-2xl bg-gradient-to-br from-[var(--color-brand)] to-yellow-400 flex items-center justify-center shadow-lg mx-auto">
                    <Icon name={feature.icon} class="w-10 h-10 text-white" />
                  </div>
                  
                  <h3 class="text-2xl md:text-3xl font-bold text-center mt-8 mb-6" style="color: var(--color-gray-900)">
                    {feature.title}
                  </h3>
                  
                  <p class="text-center text-gray-700">{feature.description}</p>
                </div>
              </div>
              
              <!-- Content Column -->
              <div class="lg:w-3/5">
                <h4 class="text-xl font-bold mb-6 text-[var(--color-brand)]">Keunggulan:</h4>
                
                {feature.bullets && feature.bullets.length > 0 && (
                  <div class="grid grid-cols-1 gap-4">
                    {feature.bullets.map((bullet, bulletIndex) => (
                      <div class="flex items-start gap-4 bg-gradient-to-r from-gray-50 to-white p-5 rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1">
                        <div class="w-8 h-8 rounded-full bg-[var(--color-brand-50)] flex items-center justify-center flex-shrink-0 mt-1">
                          <span class="text-[var(--color-brand)] font-bold">{bulletIndex + 1}</span>
                        </div>
                        <div>
                          <p class="text-lg font-medium text-gray-800">{bullet}</p>
                          <p class="text-gray-600 mt-1">
                            {[
                              "Memberikan nilai tambah signifikan untuk bisnis Anda.",
                              "Diimplementasikan dengan teknologi terbaik di industri.",
                              "Didukung oleh tim ahli berpengalaman.",
                              "Hasil yang terukur dan terbukti efektif."
                            ][bulletIndex % 4]}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>

<script>
  // Tab functionality
  document.addEventListener('DOMContentLoaded', () => {
    const tabButtons = document.querySelectorAll('.premium-tab-btn');
    const tabPanels = document.querySelectorAll('.premium-tab-panel');
    
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const index = button.getAttribute('data-index');
        
        // Update active state for buttons
        tabButtons.forEach(btn => {
          btn.setAttribute('data-active', 'false');
          btn.classList.remove('border-[var(--color-brand)]', 'bg-[var(--color-brand-50)]');
          btn.classList.add('border-gray-200', 'bg-white');
        });
        
        button.setAttribute('data-active', 'true');
        button.classList.remove('border-gray-200', 'bg-white');
        button.classList.add('border-[var(--color-brand)]', 'bg-[var(--color-brand-50)]');
        
        // Show the corresponding panel
        tabPanels.forEach(panel => {
          if (panel.getAttribute('data-index') === index) {
            panel.setAttribute('style', 'display: block');
          } else {
            panel.setAttribute('style', 'display: none');
          }
        });
      });
    });
    
    // Set initial active state
    const firstButton = document.querySelector('.premium-tab-btn');
    if (firstButton) {
      firstButton.classList.remove('border-gray-200', 'bg-white');
      firstButton.classList.add('border-[var(--color-brand)]', 'bg-[var(--color-brand-50)]');
    }
  });
</script>

<style>
  .premium-tab-btn[data-active="true"] {
    @apply border-[var(--color-brand)] bg-[var(--color-brand-50)] text-[var(--color-brand)];
  }
  
  .premium-tab-btn[data-active="false"] {
    @apply border-gray-200 bg-white text-gray-700 hover:bg-gray-50;
  }
  
  .premium-tab-btn[data-active="true"] .w-8 {
    @apply bg-[var(--color-brand-50)] text-[var(--color-brand)];
  }
  
  .premium-tab-btn[data-active="false"] .w-8 {
    @apply bg-gray-100 text-gray-500;
  }
</style>
