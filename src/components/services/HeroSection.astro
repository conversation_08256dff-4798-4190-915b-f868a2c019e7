---
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

const {
  badgeText,
  title,
  subtitle,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink,
  urgencyBadgeText, // Tambahkan prop baru
} = Astro.props;
---

<main class="relative overflow-hidden hero-main-services">
  <!-- Background design elements -->
  <BackgroundElements />
  <div class="absolute inset-0 -z-10 overflow-hidden bg-gradient-to-b from-[var(--color-brand-50)] to-white">
      <div class="absolute -right-10 -top-20 h-64 w-64 rounded-full bg-[var(--color-brand-100)] opacity-20 blur-3xl"></div>
      <div class="absolute -left-20 top-40 h-72 w-72 rounded-full bg-[var(--color-brand-100)] opacity-10 blur-3xl"></div>
  </div>
  <div class="relative z-10 w-full max-w-[60ch] mx-auto px-4">
    <div class="flex flex-col items-center text-center">

      {/* Urgency Badge - Tampilkan secara kondisional */} 
      {urgencyBadgeText && (
        <div class="urgency-badge mb-6 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-red-50 to-orange-50 px-4 py-2 text-sm font-medium text-red-700 ring-1 ring-red-200 animate-pulse">
          <Icon name="lucide:clock" class="h-4 w-4 text-red-600" />
          <span>{urgencyBadgeText}</span>
        </div>
      )}

      <h1 class="max-w-5xl mx-auto mb-2 pb-2">
        <span class="inline-block">
          <span class="z-10">{title}</span>
        </span> 
      </h1>
      
      <p class="text-lg mt-6 max-w-3xl mx-auto" style="color: var(--color-gray-700)">
        {subtitle}
      </p>
      
      <div class="mt-12 flex flex-col sm:flex-row gap-4 justify-center w-full">
        <a href={secondaryButtonLink} rel="noopener">
          <Button size="lg" style="neutral" class="flex gap-1 items-center justify-center w-full px-8 py-3">
            {secondaryButtonText}
          </Button>
        </a>
        <a href={primaryButtonLink} rel="noopener noreferrer nofollow">
          <Button size="lg" class="flex gap-1 items-center justify-center w-full px-8 py-3">
            {primaryButtonText}
          </Button>
        </a>
      </div>
    </div>
    
  </div>
</main>