---
import { Icon } from "astro-icon/components";

interface PremiumFeature {
  title: string;
  description: string;
  icon: string;
  bullets?: string[];
}

interface Props {
  title: string;
  description: string;
  features: PremiumFeature[];
}

const { title, description, features } = Astro.props;
---

<section class="py-20 relative overflow-hidden">
  <!-- Background elements -->
  <div class="absolute inset-0 -z-10 bg-gradient-to-b from-gray-900 to-gray-800"></div>
  <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
  <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
  
  <div class="max-w-7xl mx-auto px-4">
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-yellow-400/20 backdrop-blur-sm border border-yellow-400/30 mb-4">
        <Icon name="lucide:star" class="w-4 h-4 text-yellow-400" />
        <span class="text-sm font-medium text-yellow-400">Plus Features</span>
      </div>
      <h2 class="text-3xl md:text-4xl font-bold mb-6 text-white">{title}</h2>
      <p class="text-lg text-gray-300">{description}</p>
    </div>
    
    <!-- Premium Features Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {features.map((feature, index) => (
        <div class="premium-card group relative">
          <!-- Card -->
          <div class="relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden transition-all duration-500 h-full flex flex-col">
            <!-- Glowing border effect -->
            <div class="absolute inset-0 rounded-2xl p-0.5 -z-10">
              <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-yellow-400 via-[var(--color-brand)] to-yellow-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
            
            <!-- Card Header -->
            <div class="p-1">
              <div class="bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl p-6">
                <div class="flex justify-between items-start">
                  <div class="w-14 h-14 rounded-xl bg-gradient-to-br from-yellow-400 to-[var(--color-brand)] flex items-center justify-center shadow-lg">
                    <Icon name={feature.icon} class="w-7 h-7 text-white" />
                  </div>
                  <div class="bg-yellow-400/20 backdrop-blur-sm px-3 py-1 rounded-full border border-yellow-400/30">
                    <span class="text-yellow-400 text-xs font-bold">PLUS</span>
                  </div>
                </div>
                
                <h3 class="text-xl font-bold mt-6 mb-2 text-white">{feature.title}</h3>
                <p class="text-gray-300 text-sm">{feature.description}</p>
              </div>
            </div>
            
            <!-- Card Body -->
            <div class="p-6 flex-grow flex flex-col">
              {feature.bullets && feature.bullets.length > 0 && (
                <div class="space-y-4 mt-2 flex-grow">
                  {feature.bullets.map(bullet => (
                    <div class="flex items-start gap-3">
                      <div class="w-6 h-6 rounded-full bg-yellow-400/10 border border-yellow-400/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Icon name="lucide:check" class="w-3 h-3 text-yellow-400" />
                      </div>
                      <span class="text-gray-300">{bullet}</span>
                    </div>
                  ))}
                </div>
              )}
              
              <!-- No Card Footer -->
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>

<style>
  .premium-card::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -20;
    border-radius: 1rem;
    background: linear-gradient(to right, var(--color-brand), #f59e0b, var(--color-brand));
    opacity: 0;
    transition: opacity 0.5s ease;
  }
  
  .premium-card:hover::before {
    opacity: 0.7;
  }
  
  .premium-card::after {
    content: '';
    position: absolute;
    inset: 1px;
    z-index: -10;
    border-radius: 1rem;
    background: linear-gradient(to bottom, #1f2937, #111827);
  }
</style>
