---
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import { Image } from 'astro:assets'; // Import Image component if testimonial logo is passed as an image object

// Definisikan tipe untuk testimonial logo
interface Props {
  id?: string;
  title?: string;
  price?: string;
  pricePeriod?: string;
  description?: string;
  benefits?: { icon?: string; title: string; price?: string; pricePeriod?: string; description: string; bullets?: string[]; buttonText?: string; buttonLink?: string; monthlyEquivalentAnnual?: string; annualPrice?: string; monthsSaved?: number; }[];
  showPlusFeatures?: boolean;
  plusFeaturesTitle?: string;
  plusFeaturesPrice?: string;
  plusFeaturesPricePeriod?: string;
  plusFeaturesDescription?: string;
  plusFeatures?: { icon?: string; title: string; description: string; bullets?: string[] }[];
  showTestimonial?: boolean;
  testimonialQuote?: string;
  testimonialText?: string;
  testimonialLogoSrc?: ImageMetadata; // Mengharapkan objek hasil impor
  testimonialLogoAlt?: string;
  testimonialAuthor?: string;
}

const {
  id = "packages", // Default id
  title,
  price,
  pricePeriod,
  description,
  benefits = [], // Default to empty array
  showPlusFeatures = false,
  plusFeaturesTitle,
  plusFeaturesPrice,
  plusFeaturesPricePeriod,
  plusFeaturesDescription,
  plusFeatures = [], // Default to empty array
  showTestimonial = false,
  testimonialQuote,
  testimonialText,
  testimonialLogoSrc, 
  testimonialLogoAlt,
  testimonialAuthor,
} = Astro.props;
---

<section id={id} class="pt-10 md:pt-20 md:pb-10 relative">
  <!-- Garis atas gradient -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
  </div>

  <div class="max-w-7xl mx-auto px-4 relative z-10">
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="mb-4" style="color: var(--color-gray-900)">{title}</h2>
      {price && (
        <p class="text-3xl font-bold mb-4 text-[var(--color-brand)]">
          {price}
          {pricePeriod && <span class="text-lg font-medium text-gray-600">{pricePeriod}</span>}
        </p>
      )}
      <p class="text-lg" style="color: var(--color-gray-600)">
        {description}
      </p>
    </div>

    <!-- Masonry Grid with Standard Features -->
    {benefits.length > 0 && (
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 auto-rows-auto">
        {benefits.map((item) => (
          <div class="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg hover:-translate-y-2 transition-all duration-300 h-full flex flex-col">
            {item.icon && (
              <div class="w-16 h-16 rounded-2xl bg-[var(--color-brand-50)] flex items-center justify-center mb-6">
                <Icon name={item.icon} class="w-8 h-8 text-[var(--color-brand)]" />
              </div>
            )}
            
            <h3 class="text-xl font-bold mb-3" style="color: var(--color-gray-900)">{item.title}</h3>

            {/* Monthly Price */}
            {item.price && item.pricePeriod && (
              <p class="text-2xl font-bold mb-2 text-[var(--color-brand)]">
                {item.price}
                <span class="text-lg font-medium text-gray-600">{item.pricePeriod}</span>
              </p>
            )}

            {/* Annual Pricing with Discount (More Friendly) */}
            {item.annualPrice && item.monthsSaved !== undefined && (
              <div class="mb-4 -mt-0 flex flex-col items-start">
                <p class="text-xl font-bold text-gray-800"> {/* Slightly larger font for annual price */} 
                   {item.annualPrice} <span class="text-base font-medium text-gray-600">/tahun</span>
                </p>
                 <p class="text-sm text-green-600 font-semibold mt-1">Hemat {item.monthsSaved} bulan!</p> {/* Use monthsSaved */} 
              </div>
            )}

            <p class="mb-6" style="color: var(--color-gray-600)">{item.description}</p>
            
            <div class="flex-grow">
              {item.bullets && item.bullets.length > 0 && (
                <ul class="space-y-3">
                  {item.bullets.map((bullet) => (
                    <li class="flex items-center gap-3" style="color: var(--color-gray-700)">
                      <div class="rounded-full bg-white p-1 shadow-sm flex items-center justify-center border border-gray-50">
                        <Icon name="lucide:check" class="text-[var(--color-brand)] w-4 h-4 shrink-0" />
                      </div>
                      <span>{bullet}</span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
            
            {item.buttonText && item.buttonLink && (
              <div class="mt-6 pt-4 border-t border-gray-100 flex">
                <Button 
                  href={item.buttonLink}
                  variant="primary"
                  size="md"
                  class="w-full"
                >
                  {item.buttonText}
                </Button>
              </div>
            )}
          </div>
        ))}
      </div>
    )}

    <!-- Plus Features Section -->
    {showPlusFeatures && (
      <div class="mt-24 max-w-3xl mx-auto text-center">
        <h2 class="mb-4" style="color: var(--color-gray-900)">{plusFeaturesTitle}</h2>
        {plusFeaturesPrice && (
          <p class="text-3xl font-bold mb-4 text-[var(--color-brand)]">
            {plusFeaturesPrice}
            {plusFeaturesPricePeriod && <span class="text-lg font-medium text-gray-600">{plusFeaturesPricePeriod}</span>}
          </p>
        )}
        <p class="text-lg mb-12" style="color: var(--color-gray-600)">
          {plusFeaturesDescription}
        </p>
      </div>
    )}

    <!-- Plus Features Cards (different design) -->
    {showPlusFeatures && plusFeatures.length > 0 && (
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
        {plusFeatures.map((item) => (
          <div class="bg-white/90 border-2 border-gray-200 p-6 rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-2 transition-all duration-300 h-full flex flex-col relative overflow-hidden">
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-200 via-[var(--color-brand)] to-yellow-200 opacity-60 rounded-t-xl"></div>
            <div class="z-10">
              <div class="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-[var(--color-brand)] to-yellow-200 text-white text-xs font-bold mb-4 border border-yellow-300 shadow">
                <span class="mr-2">PLUS</span>
                <Icon name="lucide:star" class="w-3 h-3 text-yellow-500" />
              </div>
              {item.icon && (
                <div class="w-12 h-12 rounded-full bg-white border border-yellow-200 flex items-center justify-center mb-6 shadow">
                  <Icon name={item.icon} class="w-6 h-6 text-[var(--color-brand)]" />
                </div>
              )}
              <h3 class="text-lg font-bold mb-3 text-[var(--color-brand)]">{item.title}</h3>
              <p class="mb-6 text-gray-700">{item.description}</p>
              {item.bullets && item.bullets.length > 0 && (
                <ul class="space-y-3 mt-auto">
                  {item.bullets.map((bullet) => (
                    <li class="flex items-center gap-3 text-gray-700">
                      <div class="w-5 h-5 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0 border border-yellow-200">
                        <Icon name="lucide:plus" class="text-[var(--color-brand)] w-3 h-3 shrink-0" />
                      </div>
                      <span>{bullet}</span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        ))}
      </div>
    )}

    <!-- Testimonial Section -->
    {showTestimonial && (
      <div class="mt-16 max-w-7xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-0 items-center p-4 md:p-8">
          <!-- Highlighted Quote -->
          <div class="md:col-span-2 mb-6 md:mb-0">
            <p class="text-3xl md:text-4xl lg:text-5xl font-bold text-[var(--color-brand)] leading-tight">
              {testimonialQuote}
            </p>
          </div>
          <!-- Full Testimonial and Author -->
          <div class="space-y-6 md:col-span-3">
            <p class="text-base md:text-lg italic text-gray-700">
              {testimonialText}
            </p>
            <div class="flex items-center space-x-4">
              {/* Selalu gunakan <Image> jika testimonialLogoSrc ada */} 
              {testimonialLogoSrc && testimonialLogoAlt && (
                <Image 
                  src={testimonialLogoSrc} 
                  alt={testimonialLogoAlt} 
                  width={40}
                  height={40}
                  class="w-10 h-10 object-cover rounded-full" 
                />
              )}
              <span class="border-l border-gray-300 h-8"></span>
              <span class="text-lg font-semibold text-gray-900">{testimonialAuthor}</span>
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
</section> 