---
import { Dropdown as DropdownContainer, DropdownItems } from "astro-navbar";
import { Icon } from "astro-icon/components";
const { title, lastItem, children } = Astro.props;

// Enhanced service categorization with better organization
const serviceCategories = {
  "Pengembangan": {
    icon: "lucide:code",
    color: "blue",
    description: "Solusi pengembangan website profesional",
    items: [
      children.find(item => item.path === "/jasa/pembuatan-website"),
      children.find(item => item.path === "/jasa/redesign-website"),
      children.find(item => item.path === "/jasa/maintenance-website"),
      children.find(item => item.path === "/jasa/wordpress-hosting"),
    ]
  },
  "Optimasi": {
    icon: "lucide:zap",
    color: "green",
    description: "Perbaikan dan peningkatan performa",
    items: [
      children.find(item => item.path === "/jasa/perbaikan-website-wordpress"),
      children.find(item => item.path === "/jasa/hapus-malware-wordpress"),
      children.find(item => item.path === "/jasa/konversi-website-ke-blocks"),
      children.find(item => item.path === "/jasa/seo-onpage"),
    ]
  },
  "Migrasi": {
    icon: "lucide:move",
    color: "purple",
    description: "Layanan perpindahan dan konversi",
    items: [
      children.find(item => item.path === "/jasa/migrasi-website"),
      children.find(item => item.path === "/jasa/migrasi-wordpress-ke-astro"),
    ]
  }
};

// Filter undefined items
Object.keys(serviceCategories).forEach(category => {
  serviceCategories[category].items = serviceCategories[category].items.filter(Boolean);
});
---

<li class="relative group dropdown-container list-none">
  <DropdownContainer class="group">
    <button
      class="dropdown-trigger flex items-center gap-2 py-3 px-4 lg:py-2 lg:px-3 text-gray-700 hover:text-[var(--color-brand)] font-medium text-primary md:text-medium lg:text-sm text-sm transition-all duration-200 whitespace-nowrap rounded-xl hover:bg-gray-50/80 w-full lg:w-auto justify-between lg:justify-start"
      aria-label={`Buka menu ${title}`}
      type="button"
    >
      <span class="flex items-center gap-2">
        <Icon name="lucide:grid-3x3" class="w-4 h-4 opacity-70" />
        {title}
      </span>
      <Icon
        name="lucide:chevron-down"
        class="dropdown-arrow w-4 h-4 transition-transform duration-200 group-open:rotate-180 opacity-60"
      />
    </button>

    <DropdownItems>
      <div class="dropdown-content lg:absolute w-full lg:w-auto z-50 mt-1 lg:mt-2">

        <!-- Desktop Mega Menu -->
        <div class="hidden lg:block">
          <div class="mega-menu bg-white rounded-2xl shadow-xl border border-gray-100/50 p-6 backdrop-blur-md w-[750px] max-w-[90vw]">

            <!-- Header with description -->
            <div class="mb-6 pb-4 border-b border-gray-100">
              <h4 class="text-lg font-semibold text-gray-900 mb-1">Layanan Profesional Kami</h4>
              <p class="text-sm text-gray-600">Solusi lengkap untuk kebutuhan website dan hosting Anda</p>
            </div>

            <!-- Categories Grid -->
            <div class="grid grid-cols-3 gap-6">
              {Object.entries(serviceCategories).map(([categoryName, category]) => (
                <div class="category-section">
                  <!-- Category Header -->
                  <div class="flex items-center gap-3 mb-4">
                    <div class={`category-icon w-8 h-8 rounded-lg flex items-center justify-center bg-${category.color}-50 text-${category.color}-600`}>
                      <Icon name={category.icon} class="w-4 h-4" />
                    </div>
                    <div>
                      <h5 class="font-semibold text-sm text-gray-900">{categoryName}</h5>
                      <p class="text-xs text-gray-500 leading-relaxed">{category.description}</p>
                    </div>
                  </div>

                  <!-- Category Items -->
                  <div class="space-y-1">
                    {category.items.map((item) => (
                      <a
                        href={item.path}
                        class="service-item group item-group flex flex-col p-3 rounded-xl hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-100 hover:shadow-sm">
                        <div class="flex items-start justify-between">
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center gap-2 mb-1">
                              <span class="font-medium text-sm text-gray-900 group-hover/item:text-[var(--color-brand)] transition-colors">
                                {item.title}
                              </span>
                              {item.badge && (
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-700 ring-1 ring-red-200 animate-pulse">
                                  {item.badge}
                                </span>
                              )}
                            </div>
                            {item.description && (
                              <p class="text-xs text-gray-500 leading-relaxed line-clamp-2">
                                {item.description}
                              </p>
                            )}
                          </div>
                          <Icon
                            name="lucide:arrow-up-right"
                            class="w-3.5 h-3.5 text-gray-400 group-hover/item:text-[var(--color-brand)] transition-colors opacity-0 group-hover/item:opacity-100 flex-shrink-0 ml-2"
                          />
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              ))}
            </div>


          </div>
        </div>

        <!-- Mobile Accordion Menu -->
        <div class="lg:hidden bg-white/80 backdrop-blur-sm rounded-xl border border-gray-100/50 overflow-hidden">
          <div class="p-4 space-y-4">
            {Object.entries(serviceCategories).map(([categoryName, category]) => (
              <div class="mobile-category">
                <!-- Mobile Category Header -->
                <button
                  class="mobile-category-toggle w-full flex items-center justify-between p-3 bg-gray-50/80 hover:bg-gray-100/80 rounded-lg transition-colors"
                  type="button"
                  data-category={categoryName}>
                  <div class="flex items-center gap-3">
                    <div class={`w-7 h-7 rounded-lg flex items-center justify-center bg-${category.color}-50 text-${category.color}-600`}>
                      <Icon name={category.icon} class="w-3.5 h-3.5" />
                    </div>
                    <div class="text-left">
                      <span class="font-medium text-sm text-gray-900">{categoryName}</span>
                      <p class="text-xs text-gray-600 leading-relaxed">{category.description}</p>
                    </div>
                  </div>
                  <Icon name="lucide:chevron-down" class="w-4 h-4 text-gray-500 transition-transform duration-200" />
                </button>

                <!-- Mobile Category Items -->
                <div class="mobile-category-items mt-2 space-y-1 overflow-hidden max-h-0 transition-all duration-300">
                  {category.items.map((item) => (
                    <a
                      href={item.path}
                      class="flex items-center justify-between p-3 ml-2 text-gray-700 hover:text-[var(--color-brand)] hover:bg-white/60 rounded-lg transition-all">
                      <div class="flex-1">
                        <div class="flex items-center gap-2 mb-1">
                          <span class="font-medium text-sm">{item.title}</span>
                          {item.badge && (
                            <span class="px-2 py-0.5 text-xs font-medium bg-red-100 text-red-700 rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </div>
                        {item.description && (
                          <p class="text-xs text-gray-500 leading-relaxed">{item.description}</p>
                        )}
                      </div>
                      <Icon name="lucide:arrow-right" class="w-4 h-4 opacity-50" />
                    </a>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DropdownItems>
  </DropdownContainer>
</li>

<style>
  .dropdown-trigger:focus {
    outline: 2px solid var(--color-brand);
    outline-offset: 2px;
  }

  .mega-menu {
    animation: fadeInUp 0.2s ease-out;
    transform-origin: top;
    margin-left: auto;
    margin-right: auto;
  }

  /* Better mega menu positioning */
  @media (min-width: 1024px) {
    .dropdown-content {
      left: 50%;
      transform: translateX(-50%);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(-8px) scale(0.98);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .service-item {
    position: relative;
    overflow: hidden;
  }

  .service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--color-brand);
    transform: scaleY(0);
    transition: transform 0.2s ease;
    border-radius: 0 2px 2px 0;
  }

  .service-item:hover::before {
    transform: scaleY(1);
  }

  .category-icon {
    position: relative;
    overflow: hidden;
  }

  .category-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: currentColor;
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    opacity: 0.1;
  }

  .category-section:hover .category-icon::before {
    width: 100%;
    height: 100%;
  }

  /* Mobile accordion animations */
  .mobile-category.expanded .mobile-category-toggle svg {
    transform: rotate(180deg);
  }

  .mobile-category.expanded .mobile-category-items {
    max-height: 500px;
    padding-top: 0.5rem;
    opacity: 1;
  }

  .mobile-category-items {
    transition: max-height 0.3s ease-out, opacity 0.2s ease-out, padding 0.3s ease-out;
    opacity: 0;
  }

  .mobile-category-toggle svg {
    transition: transform 0.2s ease-out;
  }

  /* Utility classes for color variants */
  .bg-blue-50 { background-color: rgb(239 246 255); }
  .text-blue-600 { color: rgb(37 99 235); }
  .bg-green-50 { background-color: rgb(240 253 244); }
  .text-green-600 { color: rgb(22 163 74); }
  .bg-purple-50 { background-color: rgb(250 245 255); }
  .text-purple-600 { color: rgb(147 51 234); }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced focus styles */
  .service-item:focus,
  .mobile-category-toggle:focus {
    outline: 2px solid var(--color-brand);
    outline-offset: 2px;
  }

  /* Remove list bullets */
  .list-none {
    list-style: none;
  }

  /* Smooth transitions for better UX */
  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
</style>

<script>
  // Enhanced mobile accordion functionality
  document.addEventListener('DOMContentLoaded', function() {
    // Use event delegation for better compatibility
    document.addEventListener('click', function(e) {
      if (!e.target) return;
      const toggle = (e.target as Element).closest('.mobile-category-toggle');
      if (toggle) {
        e.preventDefault();
        e.stopPropagation();

        const category = toggle.closest('.mobile-category');
        if (category) {
          const wasExpanded = category.classList.contains('expanded');

          // Close all other categories in the same dropdown
          const dropdown = toggle.closest('.dropdown-content');
          if (dropdown) {
            dropdown.querySelectorAll('.mobile-category.expanded').forEach(cat => {
              if (cat !== category) {
                cat.classList.remove('expanded');
              }
            });
          }

          // Toggle current category
          category.classList.toggle('expanded', !wasExpanded);
        }
      }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
      if (!e.target || !(e.target as Element).closest('.dropdown-container')) {
        document.querySelectorAll('.mobile-category.expanded').forEach(cat => {
          cat.classList.remove('expanded');
        });
      }
    });

    // Enhanced keyboard navigation
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        document.querySelectorAll('.mobile-category.expanded').forEach(cat => {
          cat.classList.remove('expanded');
        });
      }
    });
  });
</script>
