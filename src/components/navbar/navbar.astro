---
import Container from "@/components/container.astro";
import Link from "@/components/ui/link.astro";
import Dropdown from "./dropdown.astro";
import { Astronav, MenuItems, MenuIcon } from "astro-navbar";
import { Icon } from "astro-icon/components";
import IconLogo from "@/assets/iconlogo.svg";

const menuitems = [
  {
    title: "TENTANG",
    path: "/tentang",
  },
  {
    title: "LAYANAN",
    path: "#",
    children: [
      {
        title: "Pembuatan Website",
        path: "/jasa/pembuatan-website",
        description: "Website profesional untuk bisnis Anda",
        category: "development"
      },
      {
        title: "Redesign Website",
        path: "/jasa/redesign-website",
        description: "Perbarui tampilan dan performa website Anda",
        category: "development"
      },
      {
        title: "Maintenance Website",
        path: "/jasa/maintenance-website",
        description: "Perawatan rutin untuk performa optimal",
        category: "development"
      },
      {
        title: "WordPress Hosting Premium",
        path: "/jasa/wordpress-hosting",
        badge: "New",
        description: "Hosting cepat dan aman untuk WordPress",
        category: "development"
      },
      {
        title: "Konversi WordPress ke Blocks",
        path: "/jasa/konversi-website-ke-blocks",
        description: "Modernisasi website dengan block editor",
        category: "optimization"
      },
      {
        title: "Perbaikan Website",
        path: "/jasa/perbaikan-website-wordpress",
        description: "Solusi untuk website yang bermasalah",
        category: "optimization"
      },
      {
        title: "Hapus Malware WordPress",
        path: "/jasa/hapus-malware-wordpress",
        description: "Pembersihan malware dan keamanan",
        category: "optimization"
      },
      {
        title: "Audit & Optimasi SEO On-page",
        path: "/jasa/seo-onpage",
        badge: "New",
        description: "Tingkatkan peringkat Google dengan audit SEO",
        category: "optimization"
      },
      {
        title: "Audit & Optimasi SEO On-page",
        path: "/jasa/seo-onpage",
        badge: "New",
        description: "Tingkatkan peringkat Google dengan audit SEO",
        category: "optimization"
      },
      {
        title: "Migrasi Hosting",
        path: "/jasa/migrasi-website",
        description: "Pindah hosting tanpa downtime",
        category: "migration"
      },
      {
        title: "Migrasi WordPress ke Astro",
        path: "/jasa/migrasi-wordpress-ke-astro",
        badge: "New",
        description: "Konversi ke framework modern",
        category: "migration"
      },
    ],
  },
  {
    title: "STUDI KASUS",
    path: "/studi-kasus",
  },
  {
    title: "BLOG",
    path: "/blog",
  },
];
---

<div class="fixed top-4 left-0 right-0 z-50">
  <Container>
    <div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-lg border border-gray-100/50 transition-all duration-300" id="navbar-container">
      <Astronav>
        <header class="flex items-center px-4 py-1 lg:px-6 lg:py-2">
          <!-- Logo -->
          <a href="/" class="flex items-center gap-1 group flex-shrink-0 z-10">
              <IconLogo class="w-6 h-6 text-[var(--color-brand)]" />
            <span class="font-bold text-sm sm:text-lg tracking-tight text-gray-900 whitespace-nowrap">HARUN STUDIO</span>
          </a>

          <!-- Desktop Navigation -->
          <nav class="hidden lg:flex mx-auto">
            <ul class="flex items-center gap-8">
              {
                menuitems.map((item, index) => (
                  <>
                    {item.children && (
                      <Dropdown
                        title={item.title}
                        children={item.children}
                        lastItem={index === menuitems.length - 1}
                      />
                    )}
                    {!item.children && (
                      <li>
                        <a
                          href={item.path}
                          class="nav-link relative py-2 px-3 text-gray-700 hover:text-[var(--color-brand)] font-medium text-sm transition-colors whitespace-nowrap rounded-lg hover:bg-gray-50/80">
                          {item.title}
                        </a>
                      </li>
                    )}
                  </>
                ))
              }
            </ul>
          </nav>

          <!-- Desktop CTA -->
          <div class="hidden lg:flex items-center gap-3 flex-shrink-0">
            <Link
              href="/hubungi-kami"
              size="sm"
              class="bg-[var(--color-brand)] hover:bg-[var(--color-brand-hover)] text-white shadow-sm hover:shadow-md rounded-xl transition-all duration-200 px-6 py-2.5 font-medium text-sm whitespace-nowrap flex items-center">
              <Icon name="lucide:message-circle" class="w-4 h-4 mr-2" />
              Mari Ngobrol
            </Link>
          </div>

          <!-- Mobile Menu Toggle -->
          <div class="flex lg:hidden items-center ml-auto">
            <button
              class="mobile-menu-toggle w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-xl transition-colors relative"
              id="toggle-menu"
              aria-label="Toggle menu"
              type="button">
              <MenuIcon class="w-6 h-6 text-gray-700" />
            </button>
          </div>
        </header>

        <!-- Mobile Menu -->
        <MenuItems class="mobile-menu hidden w-full lg:hidden overflow-hidden transition-all duration-300 ease-out">
          <div class="border-t border-gray-100/50 bg-gray-50/30 backdrop-blur-sm">
            <div class="px-2 py-3 space-y-1">
              {
                menuitems.map((item, index) => (
                  <>
                    {item.children && (
                      <Dropdown
                        title={item.title}
                        children={item.children}
                        lastItem={index === menuitems.length - 1}
                      />
                    )}
                    {!item.children && (
                      <a
                        href={item.path}
                        class="mobile-nav-link flex items-center py-3 px-4 text-gray-700 hover:text-[var(--color-brand)] hover:bg-white/60 rounded-xl transition-all duration-200 font-medium text-sm">
                        <span>{item.title}</span>
                        <Icon name="lucide:arrow-right" class="w-4 h-4 ml-auto opacity-50" />
                      </a>
                    )}
                  </>
                ))
              }

              <!-- Mobile CTA -->
              <div class="pt-4 mt-4 border-t border-gray-200/50">
                <Link
                  href="/hubungi-kami"
                  size="sm"
                  class="bg-[var(--color-brand)] hover:bg-[var(--color-brand-hover)] text-white shadow-sm hover:shadow-md rounded-xl transition-all duration-200 w-full justify-center py-3 font-medium flex items-center">
                  <Icon name="lucide:message-circle" class="w-4 h-4 mr-2" />
                  Mari Ngobrol
                </Link>
              </div>
            </div>
          </div>
        </MenuItems>
      </Astronav>
    </div>
  </Container>
</div>

<style>
  .mobile-menu {
    transform: translateY(-10px);
    opacity: 0;
  }

  .mobile-menu:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
  }

  .mobile-nav-link {
    backdrop-filter: blur(8px);
  }

  .mobile-menu-toggle {
    position: relative;
    overflow: hidden;
  }

  .mobile-menu-toggle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--color-brand);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    opacity: 0.1;
  }

  .mobile-menu-toggle:hover::before {
    width: 100%;
    height: 100%;
  }

  #navbar-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth scrolling enhancement */
  @media (prefers-reduced-motion: no-preference) {
    html {
      scroll-behavior: smooth;
    }
  }

  /* Focus styles for accessibility */
  .nav-link:focus,
  .mobile-nav-link:focus,
  .mobile-menu-toggle:focus {
    outline: 2px solid var(--color-brand);
    outline-offset: 2px;
  }

  /* Responsive logo text */
  @media (max-width: 640px) {
    .font-bold {
      font-size: 1rem;
      line-height: 1.25rem;
    }
  }
</style>

<script is:inline>
  document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.getElementById('toggle-menu');
    const navbarContainer = document.getElementById('navbar-container');

    if (toggleButton && navbarContainer) {
      toggleButton.addEventListener('click', function() {
        setTimeout(function() {
          const menuVisible = document.querySelector('.mobile-menu:not(.hidden)');

          if (menuVisible) {
            navbarContainer.style.borderRadius = '1rem 1rem 0.5rem 0.5rem';
            navbarContainer.style.boxShadow = '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04)';
          } else {
            navbarContainer.style.borderRadius = '1rem';
            navbarContainer.style.boxShadow = '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)';
          }
        }, 10);
      });
    }

    // Enhanced scroll behavior
    let lastScrollY = window.scrollY;
    let ticking = false;

    function updateNavbar() {
      const currentScrollY = window.scrollY;

      if (navbarContainer) {
        if (currentScrollY > 50) {
          navbarContainer.style.background = 'rgba(255, 255, 255, 0.98)';
          navbarContainer.style.backdropFilter = 'blur(20px)';
        } else {
          navbarContainer.style.background = 'rgba(255, 255, 255, 0.95)';
          navbarContainer.style.backdropFilter = 'blur(12px)';
        }
      }

      lastScrollY = currentScrollY;
      ticking = false;
    }

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateNavbar);
        ticking = true;
      }
    }

    window.addEventListener('scroll', requestTick, { passive: true });
  });
</script>
