---

interface Props {
  mobile?: string;
  message?: string;
  useApi?: boolean;
}

const { mobile = "6281291274023", message = "Hal<PERSON>, saya ingin konsultasi tentang layanan website di Harun Studio. Bisa dibantu?", useApi = true } = Astro.props as Props;
const isBlogPage = Astro.request.url.includes('/blog/');
const url = useApi ? `https://api.whatsapp.com/send?phone=${mobile}&text=${encodeURIComponent(message)}` : `https://wa.me/${mobile}`;
---

{!isBlogPage && (
  <>
    {/* wa-float now only contains the button */}
    <div id="wa-float" class="fixed bottom-4 left-1/2 -translate-x-1/2 z-50 md:bottom-8 md:right-8 md:left-auto md:translate-x-0 pointer-events-none opacity-0 transition-opacity duration-300">
      <button 
        id="whatsappButton" 
        class="flex items-center bg-white text-gray-900 px-5 py-3 rounded-full shadow-md hover:shadow-lg transition-all duration-300 ease-in-out opacity-95 hover:opacity-100 cursor-pointer border-1 border-black pointer-events-auto"
        aria-label="Konsultasi WhatsApp"
        data-umami-event="whatsapp-konsultasi-gratis"
      >
        <svg class="w-6 h-6 mr-2" version="1.1" viewBox="0 0 47.655 47.872" xmlns="http://www.w3.org/2000/svg">
          <path d="m23.729 1.8892a21.453 21.319 0 0 0-21.184 21.494 21.453 21.319 0 0 0 3.084 10.756l-0.54297 3.3184-1.3379 7.252 6.8965-2.6074 2.123-0.75391a21.453 21.319 0 0 0 11.318 3.1758 21.453 21.319 0 0 0 21.363-21.318l-0.0039-0.35547a21.453 21.319 0 0 0-21.717-20.961z" fill="#2ab318"/>
          <path d="m0 47.872 3.3661-12.292c-2.0763-3.5977-3.1685-7.6786-3.167-11.86 0.00534-13.079 10.649-23.72 23.728-23.72 6.3469 0.0026703 12.304 2.4731 16.784 6.9568 4.4796 4.4837 6.9458 10.444 6.9435 16.783-0.0057 13.079-10.651 23.722-23.728 23.722-8e-4 0 4e-4 0 0 0h-0.0103c-3.9711-0.0015-7.8728-0.9975-11.338-2.8877zm13.161-7.5927 0.7206 0.4269c3.0273 1.7967 6.4983 2.7469 10.038 2.7488h0.0076c10.87 0 19.717-8.8455 19.721-19.717 2e-3 -5.2684-2.0473-10.223-5.7708-13.949-3.7239-3.7266-8.675-5.78-13.943-5.7823-10.878 0-19.725 8.8443-19.729 19.715-0.00153 3.7254 1.0406 7.3532 3.0151 10.493l0.46921 0.7462-1.9928 7.2753z" fill="#fff"/>
          <path d="m17.997 13.804c-0.444-0.9873-0.9117-1.0071-1.3344-1.0243-0.3456-0.0148-0.7412-0.0141-1.136-0.0141-0.3952 0-1.0376 0.1488-1.5808 0.742-0.5436 0.5932-2.0756 2.0275-2.0756 4.9446 0 2.9175 2.1248 5.7362 2.4212 6.1322 0.2964 0.3956 4.1016 6.5731 10.128 8.9497 5.0083 1.9752 6.0272 1.5823 7.1144 1.4835s3.5076-1.4343 4.0016-2.8187c0.4944-1.3843 0.4944-2.5707 0.346-2.8191-0.1484-0.2468-0.5436-0.3952-1.1364-0.6916s-3.5076-1.7311-4.0512-1.9287c-0.5436-0.198-0.9388-0.2964-1.334 0.2972-0.3955 0.5928-1.5308 1.9279-1.8768 2.3231-0.346 0.3964-0.692 0.446-1.2848 0.1492-0.5928-0.2972-2.5024-0.9228-4.7676-2.9427-1.7624-1.5713-2.9525-3.5122-3.2985-4.1057-0.3456-0.5929-0.0058-0.8858 0.2605-1.2097 0.4795-0.5833 1.284-1.6319 1.4816-2.0271 0.1976-0.396 0.0988-0.742-0.0492-1.0388-0.1484-0.2964-1.3008-3.2284-1.8276-4.401z" clip-rule="evenodd" fill="#fff" fill-rule="evenodd"/>
        </svg>
        <span class="whitespace-nowrap font-bold text-sm">Konsultasi Gratis</span>
      </button>
    </div>

    {/* WhatsApp Modal moved outside of wa-float */}
    <div 
      id="whatsappModal" 
      class="hidden fixed inset-0 bg-black/50 z-[100]" 
    >
      {/* Intermediate Container for Positioning */}
      <div class="flex justify-center items-center h-full w-full p-4 md:p-0 md:justify-end md:items-end">
        
        {/* Actual Modal Content Box */}
        <div 
          class="bg-white rounded-xl shadow-lg overflow-hidden relative w-full max-w-md md:w-[400px] 
                 md:mr-8 md:mb-[calc(2rem+3.5rem+10px)] /* Adjusted margin to account for button height/spacing */
                 animate-[modalFadeIn_0.3s_ease]"
        >
          
          <!-- Panah bawah modal (hanya desktop) -->
          <div class="absolute -bottom-2 right-5 w-4 h-4 bg-white clip-path-triangle hidden md:block"></div>
          
          <div class="flex justify-between items-center px-4 py-3 border-b border-gray-100">
            <h2 class="text-lg font-bold text-gray-900 m-0">Konsultasi gratis</h2>
            <button class="bg-transparent border-none text-2xl leading-none text-gray-500 cursor-pointer" id="closeModal">&times;</button>
          </div>
          
          <div class="p-4">
            <div class="bg-gray-100 p-4 rounded-lg mb-4">
              <p class="font-bold text-base text-gray-800 mb-2">Note:</p>
              <p class="text-sm text-gray-700">mohon maaf, kami hanya bisa bekerja dengan website yang memenuhi tiga syarat di bawah ini:</p>
              
              <ul class="list-none p-0 mt-3 space-y-1">
                <li class="flex items-center">
                  <div class="w-7 h-7 flex items-center justify-center mr-3 text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><circle cx="12" cy="12" r="11" stroke="currentColor" fill="none" stroke-width="2"/><line x1="7" y1="7" x2="17" y2="17" stroke="currentColor" stroke-width="2"/><line x1="17" y1="7" x2="7" y2="17" stroke="currentColor" stroke-width="2"/></svg>
                  </div>
                  <span class="text-sm">Bukan website perjudian, game dan news</span>
                </li>
                <li class="flex items-center">
                  <div class="w-7 h-7 flex items-center justify-center mr-3 text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><circle cx="12" cy="12" r="11" stroke="currentColor" fill="none" stroke-width="2"/><circle cx="12" cy="8" r="4" stroke="currentColor" fill="none" stroke-width="2"/><line x1="7" y1="17" x2="17" y2="17" stroke="currentColor" stroke-width="2"/></svg>
                  </div>
                  <span class="text-sm">Tidak ada foto wanita</span>
                </li>
                <li class="flex items-center">
                  <div class="w-7 h-7 flex items-center justify-center mr-3 text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><circle cx="12" cy="12" r="11" stroke="currentColor" fill="none" stroke-width="2"/><path d="M8,8 Q12,4 16,8 Q20,12 16,16 Q12,20 8,16 Q4,12 8,8" stroke="currentColor" fill="none" stroke-width="2"/><line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/></svg>
                  </div>
                  <span class="text-sm">Tidak ada musik</span>
                </li>
              </ul>
            </div>
            
            <p class="text-center text-sm text-gray-800 my-4">Memenuhi syarat? Mari bicara solusi!</p>
            
            <a 
              href={url} 
              class="block bg-[#25D366] hover:bg-[#128C7E] text-white text-center py-3 px-4 rounded-full font-bold text-base my-4 transition-colors duration-300"
              target="_blank" 
              rel="noopener noreferrer nofollow"
              data-umami-event="whatsapp-modal-button"
            >
              Lanjutkan ke WhatsApp
            </a>
            
            <p class="text-center text-sm text-gray-500 m-0">Pesan masuk rata-rata kami balas &lt; 7 menit</p>
          </div>
        </div> {/* End Actual Modal Content Box */}
      </div> {/* End Intermediate Container */}
    </div> {/* End WhatsApp Modal Overlay */}
  </>
)}

<style>
  /* Hanya style untuk animasi dan clip-path yang sulit dibuat dengan Tailwind */
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .clip-path-triangle {
    clip-path: polygon(0 0, 100% 0, 50% 100%);
  }
</style>

<script>
  // Safari-friendly event handling
  function initWhatsAppButton() {
    const modalButton = document.getElementById('whatsappButton');
    const modal = document.getElementById('whatsappModal');
    const closeButton = document.getElementById('closeModal');
    
    if (!modalButton || !modal || !closeButton) {
      console.warn('WhatsApp button elements not found');
      return;
    }
    
    // Force the button to be clickable
    modalButton.style.pointerEvents = 'auto';
    
    // Show the modal when the button is clicked - direct handler
    modalButton.onclick = function(e) {
      e.preventDefault();
      e.stopPropagation();
      modal.classList.remove('hidden');
    };
    
    // Close the modal when the close button is clicked
    closeButton.onclick = function(e) {
      e.preventDefault();
      modal.classList.add('hidden');
    };
    
    // Close the modal when clicking outside the modal content
    modal.onclick = function(e) {
      const target = e.target;
      // Check if the click target is the overlay OR its direct child (the flex container)
      if (target === modal || target === modal.firstElementChild) { 
        modal.classList.add('hidden');
      }
    };
  }
  
  // Run on load and also on DOMContentLoaded (for all browsers)
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWhatsAppButton);
  } else {
    initWhatsAppButton();
  }

  // Show/hide WhatsApp button on scroll
  document.addEventListener('DOMContentLoaded', function() {
    const waFloat = document.getElementById('wa-float');
    function handleScroll() {
      if (!waFloat) return;
      if (window.scrollY > 100) {
        waFloat.classList.remove('opacity-0', 'pointer-events-none');
        waFloat.classList.add('opacity-100', 'pointer-events-auto');
      } else {
        waFloat.classList.add('opacity-0', 'pointer-events-none');
        waFloat.classList.remove('opacity-100', 'pointer-events-auto');
      }
    }
    window.addEventListener('scroll', handleScroll);
    handleScroll();
  });
</script> 