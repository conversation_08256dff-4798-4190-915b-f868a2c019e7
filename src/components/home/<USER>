---
import { getCollection } from "astro:content";
import { Image } from "astro:assets";

// Fetch and filter published blog posts
const allPosts = await getCollection("blog", ({ data }) => {
  return !data.draft && data.publishDate <= new Date();
});

// Sort by publishDate descending
allPosts.sort((a, b) => b.data.publishDate.valueOf() - a.data.publishDate.valueOf());

// Take the latest 3 posts
const posts = allPosts.slice(0, 3);
---

<section class="py-20 bg-[var(--color-brand-50)]">
  <div class="max-w-7xl mx-auto px-4">
    <div class="flex flex-col items-center text-center mb-12">
      <div class="inline-block px-4 py-1 bg-[var(--color-brand-100)] rounded-full text-[var(--color-brand)] text-sm font-medium mb-4">INSIGHT & ARTIKEL</div>
      <h2 class="mb-3" style="color: var(--color-gray-900)">Blog Terbaru</h2>
      <p class="text-lg max-w-2xl" style="color: var(--color-gray-600)">Tips dan panduan seputar website & pemasaran online</p>
    </div>
    
    <div class="grid md:grid-cols-3 gap-8">
      {posts.map((post) => (
        <div class="bg-white rounded-xl shadow-sm overflow-hidden flex flex-col h-full group hover:shadow-md transition-shadow">
          <a href={`/blog/${post.slug}/`} class="block overflow-hidden relative">
            <div class="aspect-w-16 aspect-h-9 overflow-hidden">
              <Image 
                src={post.data.image.src as any}
                alt={post.data.image.alt} 
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" 
                loading="lazy" 
                inferSize={true}
              />
            </div>
            <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
              <div class="absolute bottom-4 left-4 right-4">
                <span class="inline-block px-3 py-1 bg-white/90 rounded-full text-xs font-medium text-[var(--color-gray-900)]">
                  {post.data.category || "WordPress"}
                </span>
              </div>
            </div>
          </a>
          <div class="p-6 flex flex-col flex-1">
            <div class="mb-2 text-sm text-gray-500">
              {new Date(post.data.publishDate).toLocaleDateString('id-ID', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
            <a href={`/blog/${post.slug}/`} class="block group-hover:text-[var(--color-brand)] transition-colors">
              <h4 class="mb-3 leading-snug" style="color: var(--color-gray-900)">{post.data.title}</h4>
            </a>
            <p class="mb-4 flex-1 text-[var(--color-gray-600)]">{post.data.snippet}...</p>
            <a href={`/blog/${post.slug}/`} class="text-[var(--color-brand)] font-medium hover:underline inline-flex items-center mt-2">
              Baca Artikel
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 ml-1">
                <path fill-rule="evenodd" d="M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z" clip-rule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
      ))}
    </div>
  </div>
</section> 