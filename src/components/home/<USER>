---
// @ts-ignore
import { Icon } from "astro-icon/components";

const features = [
  {
    title: "Modern Web Development",
    description:
      "Website performa tinggi dengan teknologi terdepan seperti Astro dan Next.js, menjamin pengalaman pengguna terbaik di semua perangkat.",
    icon: "lucide:code",
    bullets: [
      "WordPress, Astro & Next.js",
      "Responsif di semua perangkat",
      "Performa & loading cepat",
    ],
  },
  {
    title: "Performance & SEO",
    description:
      "Website cepat dan teroptimasi untuk mesin pencari dengan jaminan skor PageSpeed 95+ dan perfect SEO score di Lighthouse.",
    icon: "lucide:bar-chart-2",
    bullets: [
      "Skor PageSpeed 95+",
      "SEO score 100/100",
      "Structured data schema",
    ],
  },
  {
    title: "Maintenance & Security",
    description:
      "Dukungan teknis lengkap dengan update rutin, backup, dan perlindungan keamanan untuk menjaga website Anda selalu optimal.",
    icon: "lucide:shield",
    bullets: [
      "Backup otomatis harian",
      "Pemantauan keamanan 24/7",
      "Update & perbaikan rutin",
    ],
  },
];

const solutionFeatures = [
  {
    title: "Satu Kontak untuk Semua Kebutuhan",
    description: "Tidak ada tim junior atau subkontrak—Anda bekerja langsung dengan profesional berpengalaman dari awal hingga akhir.",
    icon: "lucide:user-check"
  },
  {
    title: "Pemeliharaan Jangka Panjang",
    description: "Bukan hanya membangun website, tapi juga memelihara dan mengembangkannya untuk kebutuhan bisnis Anda yang berkembang.",
    icon: "lucide:refresh-cw"
  },
  {
    title: "Kode Bersih & Teroptimasi",
    description: "Setiap baris kode ditulis dengan standar tinggi untuk memastikan kecepatan loading dan kemudahan maintenance.",
    icon: "lucide:code"
  }
];
---

<section class="py-10 md:py-20 relative">
  <div class="absolute inset-0 bg-[var(--color-brand-50)] skew-y-3 transform-gpu -z-10"></div>
  <div class="max-w-7xl mx-auto px-4 relative z-10">
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="mb-4" style="color: var(--color-gray-900)">Dari Konsep Hingga Peluncuran</h2>
      <p class="text-lg" style="color: var(--color-gray-600)">Semua layanan web development dalam satu tangan profesional: desain, pengembangan, SEO, dan pemeliharaan jangka panjang.</p>
    </div>
    
    <!-- Layanan Utama - Timeline Style -->
    <div class="relative mb-20">
      <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-[var(--color-brand-100)] to-[var(--color-brand-50)] hidden md:block"></div>
      
      {features.map((item, index) => (
        <div class={`relative z-10 flex flex-col md:flex-row items-center md:items-start gap-8 mb-16 last:mb-0 ${index % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
          <div class="w-full md:w-1/2 flex flex-col items-center md:items-start text-center md:text-left">
            <div class="w-16 h-16 rounded-2xl bg-white shadow-md flex items-center justify-center mb-6 border border-gray-100">
              <Icon name={item.icon} class="w-8 h-8" style="color: var(--color-brand)" />
            </div>
            <h3 class="text-2xl font-bold mb-4" style="color: var(--color-gray-900)">{item.title}</h3>
            <p class="mb-6 text-lg" style="color: var(--color-gray-600)">{item.description}</p>
            <ul class="space-y-3 self-start">
              {item.bullets.map((b) => (
                <li class="flex items-center gap-3" style="color: var(--color-gray-700)">
                  <div class="rounded-full bg-white p-1 shadow-sm flex items-center justify-center border border-gray-50">
                    <Icon name="lucide:check" class="text-[var(--color-brand)] w-4 h-4 shrink-0" />
                  </div>
                  <span>{b}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div class="hidden md:block absolute left-1/2 top-10 transform -translate-x-1/2 w-12 h-12 rounded-full bg-white border-4 border-[var(--color-brand-100)] z-20"></div>
          
          <div class="w-full md:w-1/2 bg-white p-6 rounded-2xl shadow-md border border-gray-100 transform hover:-translate-y-1 transition-transform duration-300">
            {index === 0 && (
              <div class="aspect-video rounded-lg overflow-hidden bg-[var(--color-brand-50)] flex items-center justify-center mb-4">
                <div class="grid grid-cols-2 gap-4 p-4 w-full">
                  <div class="flex items-center gap-2 bg-white p-3 rounded-lg shadow-sm">
                    <Icon name="simple-icons:astro" class="w-8 h-8 text-[#ff5d01]" />
                    <div class="text-left">
                      <div class="font-bold">Astro</div>
                      <div class="text-xs text-gray-500">Performa Tinggi</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2 bg-white p-3 rounded-lg shadow-sm">
                    <Icon name="simple-icons:nextdotjs" class="w-8 h-8" />
                    <div class="text-left">
                      <div class="font-bold">Next.js</div>
                      <div class="text-xs text-gray-500">Interaktif</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2 bg-white p-3 rounded-lg shadow-sm">
                    <Icon name="simple-icons:wordpress" class="w-8 h-8 text-[#21759b]" />
                    <div class="text-left">
                      <div class="font-bold">WordPress</div>
                      <div class="text-xs text-gray-500">Fleksibel</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2 bg-white p-3 rounded-lg shadow-sm">
                    <Icon name="lucide:palette" class="w-8 h-8 text-purple-500" />
                    <div class="text-left">
                      <div class="font-bold">UI/UX</div>
                      <div class="text-xs text-gray-500">Menarik</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {index === 1 && (
              <div class="aspect-video rounded-lg overflow-hidden bg-gradient-to-br from-[var(--color-brand-50)] to-white flex items-center justify-center mb-4">
                <div class="grid grid-cols-3 gap-3 w-full max-w-xs">
                  <div class="bg-white h-28 rounded-lg shadow-sm flex flex-col items-center justify-center">
                    <div class="text-3xl font-bold text-green-700">95+</div>
                    <div class="text-xs text-gray-500 mt-1">PageSpeed</div>
                  </div>
                  <div class="bg-white h-28 rounded-lg shadow-sm flex flex-col items-center justify-center">
                    <div class="text-3xl font-bold text-[var(--color-brand)]">100</div>
                    <div class="text-xs text-gray-500 mt-1">SEO Score</div>
                  </div>
                  <div class="bg-white h-28 rounded-lg shadow-sm flex flex-col items-center justify-center">
                    <div class="text-3xl font-bold text-blue-500">A+</div>
                    <div class="text-xs text-gray-500 mt-1">Security</div>
                  </div>
                </div>
              </div>
            )}
            
            {index === 2 && (
              <div class="aspect-video rounded-lg overflow-hidden bg-gradient-to-br from-[var(--color-brand-50)] to-white flex items-center justify-center mb-4">
                <div class="flex flex-col items-center justify-center gap-3">
                  <div class="relative">
                    <div class="w-32 h-32 rounded-full border-8 border-[var(--color-brand-100)] bg-white flex items-center justify-center">
                      <Icon name="lucide:shield-check" class="w-16 h-16 text-[var(--color-brand)]" />
                    </div>
                    <div class="absolute -right-2 -bottom-2 w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center">
                      <Icon name="lucide:clock" class="w-6 h-6 text-[var(--color-brand)]" />
                    </div>
                  </div>
                  <div class="text-center font-bold text-[var(--color-brand)]">24/7 Monitoring & Support</div>
                </div>
              </div>
            )}
            
            <h4 class="text-lg font-semibold mb-2" style="color: var(--color-gray-900)">
              {index === 0 && "Teknologi Terdepan"}
              {index === 1 && "Metrik Performa"}
              {index === 2 && "Keamanan Website"}
            </h4>
            <p style="color: var(--color-gray-600)">
              {index === 0 && "Menggunakan Astro dan Next.js untuk membangun website dengan performa tinggi yang loading cepat dan mudah dikelola."}
              {index === 1 && "Skor PageSpeed dan SEO yang tinggi menjamin website Anda muncul di halaman pertama Google dan memberikan pengalaman pengguna terbaik."}
              {index === 2 && "Perlindungan menyeluruh dengan backup otomatis, pemantauan keamanan, dan update rutin untuk menjaga website Anda tetap aman."}
            </p>
          </div>
        </div>
      ))}
    </div>
    
    <!-- Keunggulan - Horizontal Tabs -->
    
  </div>
</section>
