---
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";

const caseStudies = [
  {
    title: "Peningkatan Kecepatan 240%",
    subtitle: "Transformasi Performa Win Equipment",
    description: "Optimasi menyeluruh dari WP Bakery ke GenerateBlocks yang menghasilkan peningkatan kecepatan luar biasa dan pengalaman pengguna yang lebih baik.",
    metrics: [
      { value: "240.74%", label: "Peningkatan Skor Mobile" },
      { value: "177.78%", label: "Peningkatan Skor Desktop" },
      { value: "27 → 92", label: "PageSpeed Mobile" }
    ],
    image: "https://cdn.harunstudio.com/2025/May/studi-kasus-win-equipment.webp",
    link: "/blog/dari-lambat-ke-kilat-transformasi-kecepatan-website-win-equipment",
    icon: "lucide:zap",
    badge: "OPTIMASI SPEED"
  },
  {
    title: "Redesain UX E-commerce",
    subtitle: "Optimasi Konversi Toko Online Muslimadani",
    description: "Redesain lengkap toko online dengan fokus pada optimasi konversi, fitur custom, dan pengalaman pengguna yang lebih baik untuk meningkatkan penjualan.",
    metrics: [
      { value: "1000+", label: "Baris Kode Custom" },
      { value: "100%", label: "Mobile Responsive" },
      { value: "Lebih Baik", label: "Tingkat Konversi" }
    ],
    image: "https://cdn.harunstudio.com/2025/May/studi-kasus-muslimadani.webp",
    link: "/blog/redesain-toko-online-muslimadani-id",
    icon: "lucide:shopping-cart",
    badge: "REDESAIN"
  },
  {
    title: "Migrasi Teknologi Modern",
    subtitle: "Harun Studio dari WordPress ke Astro",
    description: "Migrasi strategis dari WordPress ke Astro yang menghasilkan performa superior, biaya hosting nol, dan workflow development modern menggunakan AI.",
    metrics: [
      { value: "8x", label: "Lebih Cepat Loading" },
      { value: "100%", label: "Pengurangan Biaya" },
      { value: "54%", label: "Kurang HTTP Request" }
    ],
    image: "https://cdn.harunstudio.com/2025/May/studi-kasus-harun-studio.webp",
    link: "/blog/migrasi-harun-studio-dari-wordpress-ke-astro",
    icon: "lucide:rocket",
    badge: "MIGRASI KE ASTRO"
  }
];
---

<div class="relative py-4 md:py-10">
  <Container>
    <div class="mx-auto max-w-2xl text-center">
      <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
        Hasil Nyata untuk Bisnis Nyata
      </h2>
      <p class="mt-4 text-lg leading-8 text-gray-600">
        Dari skor PageSpeed 27 menjadi 97. Dari loading lambat menjadi 8x lebih cepat.
        Lihat bagaimana kami mentransformasi bisnis dengan hasil yang terukur.
      </p>
    </div>

    <!-- Metrics Highlight -->
    <div class="mx-auto mt-12 grid max-w-4xl grid-cols-2 gap-6 sm:grid-cols-4 lg:gap-8">
      <div class="text-center">
        <div class="text-3xl font-bold text-blue-600 sm:text-4xl">240%</div>
        <div class="mt-1 text-sm font-medium text-gray-600">Peningkatan Kecepatan</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-blue-600 sm:text-4xl">8x</div>
        <div class="mt-1 text-sm font-medium text-gray-600">Lebih Cepat Loading</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-blue-600 sm:text-4xl">100%</div>
        <div class="mt-1 text-sm font-medium text-gray-600">Pengurangan Biaya</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-blue-600 sm:text-4xl">Lebih Baik</div>
        <div class="mt-1 text-sm font-medium text-gray-600">Tingkat Konversi</div>
      </div>
    </div>

    <!-- Case Studies Grid -->
    <div class="mx-auto mt-16 grid max-w-6xl gap-8 lg:grid-cols-3">
      {caseStudies.map((study) => (
        <div class="group relative overflow-hidden rounded-2xl bg-white shadow-sm ring-1 ring-gray-200 transition-all duration-300 hover:shadow-lg hover:ring-gray-300">
          <!-- Badge -->
          <div class="absolute left-4 top-4 z-10">
            <span class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
              {study.badge}
            </span>
          </div>

          <!-- Image -->
          <div class="aspect-[16/9] overflow-hidden bg-gray-50">
            <img
              src={study.image}
              alt={study.subtitle}
              class="h-full w-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
            />
          </div>

          <!-- Content -->
          <div class="p-6">
            <div class="flex items-center gap-2 text-blue-600">
              <Icon name={study.icon} class="h-5 w-5" />
              <h3 class="text-lg font-semibold">{study.title}</h3>
            </div>

            <h4 class="mt-2 text-xl font-bold text-gray-900">{study.subtitle}</h4>

            <p class="mt-3 text-sm leading-6 text-gray-600">
              {study.description}
            </p>

            <!-- Metrics -->
            <div class="mt-4 grid grid-cols-3 gap-4">
              {study.metrics.map((metric) => (
                <div class="text-center">
                  <div class="text-lg font-bold text-gray-900">{metric.value}</div>
                  <div class="text-xs text-gray-500">{metric.label}</div>
                </div>
              ))}
            </div>

            <!-- CTA -->
            <div class="mt-6">
              <a
                href={study.link}
                class="inline-flex items-center gap-2 text-sm font-semibold text-blue-600 transition-colors hover:text-blue-500"
              >
                Baca Studi Kasus Lengkap
                <Icon name="lucide:arrow-right" class="h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>

    <!-- Bottom CTA -->
    <div class="mt-12 text-center">
      <a
        href="/blog"
        class="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-sm font-semibold text-white shadow-sm transition-colors hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
      >
        Lihat Lebih Banyak Kisah Sukses
        <Icon name="lucide:arrow-right" class="h-4 w-4" />
      </a>
    </div>
  </Container>
</div>
