---
import { Icon } from "astro-icon/components";

const mainServices = [
  {
    title: "Website Development",
    description: "Website profesional dengan teknologi WordPress, Astro, atau Next.js sesuai kebutuhan bisnis Anda. Performa tinggi, SEO friendly, dan mudah di<PERSON>.",
    icon: "lucide:layout",
    features: ["WordPress", "Astro", "Next.js", "Design UI/UX", "Responsif"],
    link: "/jasa/pembuatan-website",
    color: "var(--color-brand)"
  },
  {
    title: "Aplikasi Website",
    description: "Aplikasi web untuk otomatisasi proses bisnis, manajemen data, dan kebutuhan khusus perusahaan <PERSON>a. Dengan fitur yang disesuaikan kebutuhan spesifik.",
    icon: "lucide:app-window",
    features: ["Coming Soon", "Dashboard Admin", "Integrasi API", "Custom Features"],
    link: "#",
    color: "#2652E3",
    comingSoon: true
  },
  {
    title: "Maintenance Website",
    description: "Layanan pemeliharaan rutin untuk memastikan website Anda selalu aman, cepat, dan up-to-date. <PERSON><PERSON> dari update konten hingga perbaikan bug.",
    icon: "lucide:settings",
    features: ["Update Rutin", "Backup Otomatis", "Monitoring Keamanan", "Optimasi Performa"],
    link: "/jasa/maintenance-website",
    color: "#2652E3"
  }
];

const quickServices = [
  {
    title: "Perbaikan Website",
    description: "Memperbaiki error, bug, atau masalah teknis pada website Anda dengan cepat dan efektif.",
    icon: "lucide:wrench",
    timeframe: "< 30 menit",
    link: "/jasa/perbaikan-website-wordpress"
  },
  {
    title: "Hapus Malware",
    description: "Membersihkan website dari malware, virus, dan serangan hacker untuk memulihkan keamanan website.",
    icon: "lucide:shield",
    timeframe: "< 3 jam",
    link: "/jasa/hapus-malware-wordpress"
  },
  {
    title: "Migrasi Hosting",
    description: "Memindahkan website Anda ke hosting baru dengan zero downtime dan tanpa kehilangan data.",
    icon: "lucide:cloud",
    timeframe: "< 30 menit",
    link: "/jasa/migrasi-website"
  },
  {
    title: "Migrasi ke WordPress",
    description: "Konversi website dari platform lain ke WordPress dengan tampilan & fungsi yang sama atau lebih baik.",
    icon: "simple-icons:wordpress",
    timeframe: "3-7 hari",
    link: "/jasa/konversi-website-ke-blocks/"
  },
  {
    title: "Migrasi ke Astro",
    description: "Upgrade website ke Astro framework untuk performa super cepat dan SEO score sempurna.",
    icon: "simple-icons:astro",
    timeframe: "5-10 hari",
    link: "/jasa/migrasi-wordpress-ke-astro"
  }
];
---

<section class="py-4 md:py-10">
  <div class="max-w-7xl mx-auto px-4">
    <!-- Main Services Header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="mb-4" style="color: var(--color-gray-900)">Solusi Website Bisnis Anda</h2>
      <p class="text-lg" style="color: var(--color-gray-600)">Dari pengembangan hingga pemeliharaan, kami menyediakan layanan lengkap untuk kebutuhan website Anda.</p>
    </div>
    
    <!-- Main Services (Cards with Hover) -->
    <div class="grid md:grid-cols-3 gap-8 mb-24">
      {mainServices.map((service) => (
        <div class="group bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:translate-y-[-4px] h-full flex flex-col">
          <!-- Card Header with Icon -->
          <div class="p-6 pb-0">
            <div class="flex items-center gap-4 mb-5">
              <div class="w-14 h-14 rounded-xl flex items-center justify-center" style={`background-color: ${service.color}15`}>
                <Icon name={service.icon} class="w-7 h-7" style={`color: ${service.color}`} />
              </div>
              {service.comingSoon && 
                <span class="px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">Coming Soon</span>
              }
            </div>
            
            <h3 class="text-xl font-bold mb-3" style="color: var(--color-gray-900)">{service.title}</h3>
            <p class="mb-6" style="color: var(--color-gray-600)">{service.description}</p>
          </div>
          
          <!-- Card Footer with Features and Link -->
          <div class="p-6 pt-0 mt-auto">
            <div class="flex flex-wrap gap-2 mb-5">
              {service.features.map((feature) => (
                <span class="px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">{feature}</span>
              ))}
            </div>
            
            <div class="border-t border-gray-100 pt-4">
              {service.link && !service.comingSoon ? (
                <a href={service.link} class="inline-flex items-center text-sm font-medium group-hover:underline" style={`color: ${service.color}`}>
                  Lihat Detail
                  <Icon name="lucide:arrow-right" class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </a>
              ) : (
                <span class="inline-flex items-center text-sm font-medium opacity-60 cursor-not-allowed">
                  {service.comingSoon ? 'Pelajari Lebih Lanjut' : 'Lihat Detail'}
                  <Icon name="lucide:arrow-right" class="w-4 h-4 ml-1" />
                </span>
              )}
            </div>
          </div>
          
          <!-- Accent Top Border -->
          <div class="h-1 w-full" style={`background-color: ${service.color}`}></div>
        </div>
      ))}
    </div>
    
    <!-- Quick Services Section -->
    <div class="relative">
      <!-- Section Header with Background -->
      <div class="relative rounded-3xl p-8 mb-12">
        <!-- Background gradient element -->
        <div class="absolute inset-0 -z-10 rounded-3xl bg-gradient-to-br from-[var(--color-brand-50)] to-white"></div>
        
        <div class="max-w-3xl mx-auto text-center mb-0">
          <h2 class="mb-4" style="color: var(--color-gray-900)">Layanan Sekali Waktu</h2>
          <p class="text-lg" style="color: var(--color-gray-600)">Solusi cepat untuk kebutuhan spesifik website Anda tanpa perlu berlangganan.</p>
        </div>
      </div>
      
      <!-- Quick Services Cards Grid -->
      <div class="grid gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 -mt-4">
        {quickServices.map((service) => (
          <a
            href={service.link ? service.link : `/services/quick/${service.title.toLowerCase().replace(/\s+/g, '-')}`}
            class="group bg-white rounded-xl border border-gray-100 hover:border-[var(--color-brand-200)] shadow-sm hover:shadow-md transition-all p-5 relative overflow-hidden block focus:outline-none focus:ring-2 focus:ring-[var(--color-brand)]"
            style="text-decoration: none;"
          >
            {/* Icon */}
            <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-[var(--color-brand-50)] to-[var(--color-brand-100)] flex items-center justify-center mb-4">
              <Icon name={service.icon} class="w-6 h-6 text-[var(--color-brand)]" />
            </div>
            {/* Content */}
            <h3 class="text-lg font-bold mb-2" style="color: var(--color-gray-900)">{service.title}</h3>
            <p class="text-sm mb-4" style="color: var(--color-gray-600)">{service.description}</p>
            {/* Footer */}
            <div class="flex justify-between items-center mt-auto">
              <span class="text-xs font-medium flex items-center gap-1 text-[var(--color-brand)]">
                <Icon name="lucide:clock" class="w-3 h-3" />
                {service.timeframe}
              </span>
              <span class="text-xs font-medium text-gray-900 flex items-center group-hover:text-[var(--color-brand)] transition-colors">
                Detail
                <Icon name="lucide:chevron-right" class="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" />
              </span>
            </div>
            {/* Background Pattern */}
            <div class="absolute top-0 right-0 w-24 h-24 bg-[var(--color-brand-50)] rounded-full -mr-12 -mt-12 opacity-0 group-hover:opacity-20 transition-opacity"></div>
          </a>
        ))}
      </div>
    </div>
  </div>
</section> 