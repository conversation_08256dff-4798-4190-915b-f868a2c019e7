---
// Testimonial section for homepage
import { Icon } from "astro-icon/components";
import maistroAudioLogo from "@/assets/client-logo/maistroaudio-black.svg";
import muslimadaniLogo from "@/assets/client-logo/black-mm.svg";
---

<section class="py-4 md:py-10 relative overflow-hidden">  
  <div class="max-w-7xl mx-auto px-4 relative z-10">
    <div class="flex flex-col items-center text-center mb-14">
      <h2 class="mb-4" style="color: var(--color-gray-900)">Pendapat Klien Ka<PERSON></h2>
      <p class="text-lg max-w-2xl" style="color: var(--color-gray-600)">Pengalaman mereka yang telah mempercayakan website bisnisnya kepada Harun Studio</p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
      <!-- Testimonial 1 -->
      <div class="bg-white rounded-xl shadow-lg overflow-hidden h-full border border-gray-100 transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
        <div class="p-8 flex flex-col h-full">
          <div class="flex justify-between items-center mb-6">
            <img src={maistroAudioLogo.src} alt="Maistro Audio" class="h-7" loading="lazy" width={maistroAudioLogo.width} height={maistroAudioLogo.height} />
            
            <div class="flex gap-1">
              {Array(5).fill(null).map(() => (
                <Icon name="lucide:star" class="w-5 h-5 text-yellow-400 fill-current" />
              ))}
            </div>
          </div>
          
          <blockquote class="text-lg mb-auto text-gray-700 relative">
            <Icon name="lucide:quote" class="absolute -top-2 -left-1 w-8 h-8 text-[var(--color-brand-100)] opacity-40" />
            <p class="relative z-10 pl-2">
              "Pengalaman layanan jasa pembuatan website dengan Harun Studio sangat memuaskan. Seperti rekan kerja yang kompeten dan dapat diandalkan, enak untuk diajak diskusi, dan memiliki etos yang baik dan profesional. Terima Kasih Harun Studio, sangat merekomendasikan!"
            </p>
          </blockquote>
          
          <div class="border-t border-gray-100 pt-6 mt-6 flex items-center gap-4">
            <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
              <Icon name="lucide:user" class="w-6 h-6" />
            </div>
            <div>
              <div class="font-bold text-gray-900">Maikel Imanuel</div>
              <div class="text-[var(--color-brand)] text-sm">Founder, MaistroAudio.com</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 2 -->
      <div class="bg-white rounded-xl shadow-lg overflow-hidden h-full border border-gray-100 transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
        <div class="p-8 flex flex-col h-full">
          <div class="flex justify-between items-center mb-6">
            <img src={muslimadaniLogo.src} alt="Muslimadani" class="h-7" loading="lazy" width={muslimadaniLogo.width} height={muslimadaniLogo.height} />
            
            <div class="flex gap-1">
              {Array(5).fill(null).map(() => (
                <Icon name="lucide:star" class="w-5 h-5 text-yellow-400 fill-current" />
              ))}
            </div>
          </div>
          
          <blockquote class="text-lg mb-auto text-gray-700 relative">
            <Icon name="lucide:quote" class="absolute -top-2 -left-1 w-8 h-8 text-[var(--color-brand-100)] opacity-40" />
            <p class="relative z-10 pl-2">
              "Muslimadani memakai jasa Harun Studio untuk pembuatan website dan sekaligus maintenance sistem setiap bulannya. Alhamdulillah website memuaskan. Dan untuk jasa maintenance website nya sendiri juga memuaskan. Loading, safety dan uptime selalu terjaga rapi. Jadi website kami insyaAllah loading cepat, aman dan selalu ready. Jazaakumullah khayran Harun Studio."
            </p>
          </blockquote>
          
          <div class="border-t border-gray-100 pt-6 mt-6 flex items-center gap-4">
            <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
              <Icon name="lucide:user" class="w-6 h-6" />
            </div>
            <div>
              <div class="font-bold text-gray-900">Hikmah</div>
              <div class="text-[var(--color-brand)] text-sm">Muslimadani.id</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section> 