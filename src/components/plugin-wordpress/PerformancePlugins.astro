---
import { Icon } from "astro-icon/components";
---

<section id="performance" class="mb-24">
  <div class="border-l-4 border-[var(--color-brand)] pl-4 mb-12">
    <h2 class="text-3xl font-bold text-gray-900">Plugin Optimasi Kecepatan dan Performa</h2>
    <p class="text-gray-600 mt-2">Tingkatkan kecepatan loading dan performa website WordPress Anda</p>
  </div>

  <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
    <!-- Asset CleanUp -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all hover:shadow-md">
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">Asset CleanUp</h3>
        <p class="text-gray-600 mb-4">Memungkinkan Anda mempercepat website dengan cara yang cerdas, yaitu dengan menonaktifan CSS/JS tertentu yang tidak diperlukan.</p>
        
        <ul class="space-y-2 mb-6">
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Pilih dan nonaktifkan skrip dan CSS per halaman/post</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Pengurangan jumlah permintaan HTTP dan ukuran halaman</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Memiliki fitur lainnya, seperti optimasi CSS/JS</span>
          </li>
        </ul>
        
        <a href="https://wordpress.org/plugins/wp-asset-clean-up/" class="inline-flex items-center text-[var(--color-brand)] hover:text-[var(--color-brand-hover)] font-medium" target="_blank" rel="noopener noreferrer">
          Pelajari selengkapnya
          <Icon name="lucide:arrow-right" class="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>

    <!-- Perfmatters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all hover:shadow-md">
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">Perfmatters</h3>
        <p class="text-gray-600 mb-4">Plugin yang meningkatkan performa situs dengan mengurangi beban resource dan memungkinkan pengaturan yang lebih detail, ideal jika Anda tidak menggunakan Litespeed cache.</p>
        
        <ul class="space-y-2 mb-6">
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Mengurangi beban resource untuk kecepatan lebih</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">UI yang ramah pengguna dengan fitur lanjutan</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Cocok sebagai alternatif untuk kombinasi Litespeed cache dan Asset Cleanup</span>
          </li>
        </ul>
        
        <a href="https://perfmatters.io/" class="inline-flex items-center text-[var(--color-brand)] hover:text-[var(--color-brand-hover)] font-medium" target="_blank" rel="noopener noreferrer">
          Pelajari selengkapnya
          <Icon name="lucide:arrow-right" class="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>

    <!-- GenerateBlocks -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all hover:shadow-md">
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">GenerateBlocks</h3>
        <p class="text-gray-600 mb-4">Page Builder (Gutenberg like) yang ringan dan cepat. Sangat cocok untuk meningkatkan performa website Anda dengan integrasi yang sempurna dengan tema GeneratePress.</p>
        
        <ul class="space-y-2 mb-6">
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Blok yang ringan dan efisien</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Pengganti ideal untuk page builder seperti Elementor dan WP Bakery</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Kami menggunakannya di Harun Studio dan seluruh website klien</span>
          </li>
        </ul>
        
        <a href="https://wordpress.org/plugins/generateblocks/" class="inline-flex items-center text-[var(--color-brand)] hover:text-[var(--color-brand-hover)] font-medium" target="_blank" rel="noopener noreferrer">
          Pelajari selengkapnya
          <Icon name="lucide:arrow-right" class="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>
  </div>
</section> 