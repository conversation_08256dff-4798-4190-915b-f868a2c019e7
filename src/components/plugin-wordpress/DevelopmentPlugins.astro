---
import { Icon } from "astro-icon/components";
---

<section id="development" class="mb-24">
  <div class="border-l-4 border-[var(--color-brand)] pl-4 mb-12">
    <h2 class="text-3xl font-bold text-gray-900">Plugin Pengembangan dan <PERSON></h2>
    <p class="text-gray-600 mt-2">Kustomisasi dan kembangkan website WordPress Anda dengan mudah</p>
  </div>

  <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
    <!-- ACF (Advanced Custom Fields) -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all hover:shadow-md">
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">Advanced Custom Fields</h3>
        <p class="text-gray-600 mb-4"><PERSON><PERSON>, Anda bisa menambahkan field kustom pada WordPress, meningkatkan fleksibilitas dan kontrol dalam menampilkan konten khusus.</p>
        
        <ul class="space-y-2 mb-6">
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Buat field kustom untuk postingan dan halaman</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Integrasi mudah dengan tema</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Membuat custom post type</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Mendukung berbagai jenis field</span>
          </li>
        </ul>
        
        <a href="https://wordpress.org/plugins/advanced-custom-fields/" class="inline-flex items-center text-[var(--color-brand)] hover:text-[var(--color-brand-hover)] font-medium" target="_blank" rel="noopener noreferrer">
          Pelajari selengkapnya
          <Icon name="lucide:arrow-right" class="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>

    <!-- Code Snippets -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all hover:shadow-md">
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">Code Snippets</h3>
        <p class="text-gray-600 mb-4">Plugin ini menyediakan solusi yang terorganisir dan efisien untuk menyimpan dan mengelola potongan kode kustom, seperti PHP, HTML, CSS, atau JavaScript.</p>
        
        <ul class="space-y-2 mb-6">
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Kelola kode kustom dengan mudah</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Tidak perlu mengedit file tema</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Cocok untuk penambahan fungsionalitas situs</span>
          </li>
        </ul>
        
        <a href="https://wordpress.org/plugins/code-snippets/" class="inline-flex items-center text-[var(--color-brand)] hover:text-[var(--color-brand-hover)] font-medium" target="_blank" rel="noopener noreferrer">
          Pelajari selengkapnya
          <Icon name="lucide:arrow-right" class="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>

    <!-- Simple CSS -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all hover:shadow-md">
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">Simple CSS</h3>
        <p class="text-gray-600 mb-4">Plugin yang memudahkan Anda dalam menambahkan dan mengelola CSS khusus untuk halaman atau post tertentu, sangat berguna untuk penyesuaian visual situs Anda.</p>
        
        <ul class="space-y-2 mb-6">
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Custom CSS per halaman atau post</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Mudah digunakan</span>
          </li>
          <li class="flex items-start">
            <Icon name="lucide:check-circle" class="text-green-500 w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
            <span class="text-gray-700">Cocok untuk semua tema</span>
          </li>
        </ul>
        
        <a href="https://wordpress.org/plugins/simple-css/" class="inline-flex items-center text-[var(--color-brand)] hover:text-[var(--color-brand-hover)] font-medium" target="_blank" rel="noopener noreferrer">
          Pelajari selengkapnya
          <Icon name="lucide:arrow-right" class="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>
  </div>
</section> 