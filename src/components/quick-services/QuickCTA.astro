---
import Link from "@/components/ui/link.astro";
import { Icon } from "astro-icon/components";
import { Image } from 'astro:assets';

// Definisikan tipe props untuk kejelasan (opsional, tapi bagus)
interface Props {
  badge?: string;
  title: string;
  subtitle: string;
  buttonText: string;
  buttonLink: string;
  showWhatsAppIcon?: boolean;
  imageSrc: ImageMetadata; // Mengharapkan objek hasil impor dari `astro:assets`
  imageAlt: string;
  showImage?: boolean;
}

const {
  badge,
  title,
  subtitle,
  buttonText,
  buttonLink,
  showWhatsAppIcon = true,
  imageSrc, 
  imageAlt,
  showImage = false
} = Astro.props;
---

<section class="py-16 relative overflow-hidden">
  <div class="absolute inset-0 -z-10">
    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
    <div class="absolute -right-32 -top-32 w-96 h-96 bg-[var(--color-brand-50)] rounded-full opacity-30 blur-3xl"></div>
    <div class="absolute -left-32 bottom-0 w-96 h-96 bg-[var(--color-brand-50)] rounded-full opacity-30 blur-3xl"></div>
  </div>
  
  <div class="max-w-7xl mx-auto px-4">
    <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl shadow-xl overflow-hidden">
      <div class="flex flex-col md:flex-row">
        <!-- Left: Content -->
        <div class={`flex flex-col ${showImage ? 'md:w-1/2 items-start text-left' : 'items-center text-center w-full'} p-10 md:p-14 relative z-10 overflow-hidden`}>
          <!-- Decorative elements -->
          <div class="absolute -right-16 -top-16 w-56 h-56 rounded-full bg-gradient-to-r from-[var(--color-brand)] to-blue-600 opacity-20 blur-3xl"></div>
          
          {badge && (
            <div class="inline-block mb-6 px-4 py-1 bg-white/10 backdrop-blur-sm rounded-full">
              <span class="text-white/80 text-sm">{badge}</span>
            </div>
          )}
          
          <h2 class="mb-6 text-white text-3xl font-bold">{title}</h2>
          
          <p class="text-lg mb-8 text-gray-300 max-w-2xl">
            {subtitle}
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4">
            <Link href={buttonLink} class="border border-white/20 hover:bg-white/10 text-white rounded-lg inline-flex items-center justify-center px-8 py-3 transition-colors" rel="noopener noreferrer nofollow">
              {showWhatsAppIcon && (
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" class="mr-2" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10.0025 0H9.9975C4.48375 0 0 4.485 0 10C0 12.1875 0.705 14.215 1.90375 15.8612L0.6575 19.5763L4.50125 18.3475C6.0825 19.395 7.96875 20 10.0025 20C15.5162 20 20 15.5138 20 10C20 4.48625 15.5162 0 10.0025 0Z" fill="#4CAF50"/>
                  <path d="M15.8212 14.1212C15.5799 14.8025 14.6224 15.3675 13.8587 15.5325C13.3362 15.6437 12.6537 15.7325 10.3562 14.78C7.41744 13.5625 5.52494 10.5763 5.37744 10.3825C5.23619 10.1887 4.18994 8.80123 4.18994 7.36623C4.18994 5.93123 4.91869 5.23248 5.21244 4.93248C5.45369 4.68623 5.85244 4.57373 6.23494 4.57373C6.35869 4.57373 6.46994 4.57998 6.56994 4.58498C6.86369 4.59748 7.01119 4.61498 7.20494 5.07873C7.44619 5.65998 8.03369 7.09498 8.10369 7.24248C8.17494 7.38998 8.24619 7.58998 8.14619 7.78373C8.05244 7.98373 7.96994 8.07248 7.82244 8.24248C7.67494 8.41248 7.53494 8.54248 7.38744 8.72498C7.25244 8.88373 7.09994 9.05373 7.26994 9.34748C7.43994 9.63498 8.02744 10.5937 8.89244 11.3637C10.0087 12.3575 10.9137 12.675 11.2374 12.81C11.4787 12.91 11.7662 12.8862 11.9424 12.6987C12.1662 12.4575 12.4424 12.0575 12.7237 11.6637C12.9237 11.3812 13.1762 11.3462 13.4412 11.4462C13.7112 11.54 15.1399 12.2462 15.4337 12.3925C15.7274 12.54 15.9212 12.61 15.9924 12.7337C16.0624 12.8575 16.0624 13.4387 15.8212 14.1212Z" fill="#FAFAFA"/>
                </svg>
              )}
              {buttonText}
            </Link>
          </div>
        </div>
        
        <!-- Right: Image (only shown if showImage is true) -->
        {showImage && imageSrc && imageAlt && (
          <div class="hidden md:block md:w-1/2 relative">
            <Image 
              src={imageSrc} 
              alt={imageAlt} 
              class="absolute inset-0 w-full h-full object-cover" 
              loading="lazy"
              width={imageSrc.width}
              height={imageSrc.height}
            />
            
            <!-- Overlay gradient -->
            <div class="absolute inset-0 bg-gradient-to-r from-gray-900 to-transparent opacity-20"></div>
          </div>
        )}
      </div>
    </div>
  </div>
</section> 