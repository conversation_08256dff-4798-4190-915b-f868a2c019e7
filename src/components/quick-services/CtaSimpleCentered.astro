---
import { Icon } from "astro-icon/components";
import Link from "@/components/ui/link.astro";

const {
  title,
  subtitle,
  buttonText,
  buttonLink,
  icon = "", // Opsional
  buttonColor = "primary", // Default primary, bisa juga "neutral", "outline", "inverted", "muted"
} = Astro.props;
---

<div class="max-w-7xl mx-auto px-4">
  <div class="text-center">
    {title && <h2 class="mb-4 text-2xl md:text-3xl font-bold" style="color: var(--color-gray-900)">{title}</h2>}
    
    {subtitle && <p class="mb-6 text-lg md:max-w-3xl mx-auto" style="color: var(--color-gray-600)">{subtitle}</p>}
    
    <!-- Slot setelah subtitle (sebelum button) -->
    <slot name="after_subtitle" />
    
    {buttonText && buttonLink && (
      <div class="mt-8">
        <Link 
          href={buttonLink} 
          style={buttonColor}
          size="lg"
          target="_blank" 
          rel="noopener noreferrer nofollow"
          class="inline-flex items-center"
        >
          {icon && <Icon name={icon} class="w-5 h-5 mr-2" />}
          {buttonText}
        </Link>
      </div>
    )}
    
    <!-- Slot setelah button (footer) -->
    <slot name="after_button" />
  </div>
</div> 