---
import { Icon } from "astro-icon/components";
import Link from "@/components/ui/link.astro";

const { title, subtitle, steps = [], cta = null } = Astro.props;
---

<section class="py-16 md:py-24 relative">
  <div class="absolute inset-0 bg-[var(--color-brand-50)] skew-y-3 transform-gpu -z-10 opacity-60"></div>
  
  <div class="max-w-7xl mx-auto px-4 relative z-10">
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="mb-4 text-3xl md:text-4xl font-bold" style="color: var(--color-gray-900)">{title}</h2>
      <p class="text-lg md:text-xl" style="color: var(--color-gray-600)">{subtitle}</p>
    </div>
    
    <div class="relative mb-20">
      <!-- Vertical Timeline Line -->
      <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-[var(--color-brand-100)] to-[var(--color-brand-50)] hidden md:block"></div>
      
      {steps.map((step, index) => (
        <div class={`relative z-10 flex flex-col md:flex-row items-center md:items-start gap-8 mb-16 last:mb-0 ${index % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
          <div class="w-full md:w-1/2 flex flex-col items-center md:items-start text-center md:text-left">
            <div class="w-16 h-16 rounded-2xl bg-white shadow-md flex items-center justify-center mb-6 border border-gray-100">
              <Icon name={step.icon} class="w-8 h-8" style="color: var(--color-brand)" />
            </div>
            <h3 class="text-2xl font-bold mb-4" style="color: var(--color-gray-900)">{step.title}</h3>
            <p class="mb-6 text-lg" style="color: var(--color-gray-600)">{step.description}</p>
            
            {step.bullets && step.bullets.length > 0 && (
              <ul class="space-y-3 self-start">
                {step.bullets.map((bullet) => (
                  <li class="flex items-center gap-3" style="color: var(--color-gray-700)">
                    <div class="rounded-full bg-white p-1 shadow-sm flex items-center justify-center border border-gray-50">
                      <Icon name="lucide:check" class="text-[var(--color-brand)] w-4 h-4 shrink-0" />
                    </div>
                    <span>{bullet}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
          
          <!-- Timeline Node -->
          <div class="hidden md:block absolute left-1/2 top-10 transform -translate-x-1/2 w-12 h-12 rounded-full bg-white border-4 border-[var(--color-brand-100)] z-20 process-marker">
            <div class="w-full h-full rounded-full flex items-center justify-center">
              <span class="text-lg font-bold text-[var(--color-brand)]">{index + 1}</span>
            </div>
          </div>
          
          <!-- Content Card -->
          <div class="w-full md:w-1/2 bg-white p-6 rounded-2xl shadow-md border border-gray-100 transform hover:-translate-y-1 transition-transform duration-300">
            <!-- Process Illustration based on step index -->
            <div class="aspect-video rounded-lg overflow-hidden bg-gradient-to-br from-[var(--color-brand-50)] to-white flex items-center justify-center mb-4">
              {index === 0 && (
                <div class="flex flex-col items-center justify-center">
                  <div class="relative">
                    <div class="w-24 h-24 rounded-full bg-white shadow-sm flex items-center justify-center border-2 border-[var(--color-brand-100)]">
                      <Icon name={step.icon} class="w-12 h-12 text-[var(--color-brand)]" />
                    </div>
                    <div class="absolute -right-2 -bottom-2 w-10 h-10 rounded-full bg-white shadow-md flex items-center justify-center">
                      <span class="text-lg font-bold text-[var(--color-brand)]">1</span>
                    </div>
                  </div>
                  <div class="mt-3 font-medium text-[var(--color-gray-700)]">Langkah Pertama</div>
                </div>
              )}
              
              {index === 1 && (
                <div class="flex items-center justify-center gap-4">
                  <div class="flex flex-col items-center opacity-50">
                    <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center border border-gray-200">
                      <span class="text-lg font-bold text-gray-400">1</span>
                    </div>
                    <div class="h-1 w-12 bg-gradient-to-r from-gray-200 to-[var(--color-brand-200)] mt-2"></div>
                  </div>
                  
                  <div class="flex flex-col items-center">
                    <div class="w-20 h-20 rounded-full bg-white shadow-md flex items-center justify-center border-2 border-[var(--color-brand-200)]">
                      <Icon name={step.icon} class="w-10 h-10 text-[var(--color-brand)]" />
                    </div>
                    <div class="h-1 w-12 bg-gradient-to-r from-[var(--color-brand-200)] to-gray-200 mt-2"></div>
                  </div>
                  
                  <div class="flex flex-col items-center opacity-50">
                    <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center border border-gray-200">
                      <span class="text-lg font-bold text-gray-400">3</span>
                    </div>
                  </div>
                </div>
              )}
              
              {index === 2 && (
                <div class="flex flex-col items-center justify-center">
                  <div class="flex items-center gap-2">
                    <Icon name="lucide:check-circle" class="w-10 h-10 text-green-500" />
                    <span class="text-2xl font-bold text-[var(--color-brand)]">Selesai!</span>
                  </div>
                  <div class="w-32 h-1 bg-gradient-to-r from-[var(--color-brand-100)] to-green-500 mt-3 rounded-full"></div>
                </div>
              )}
            </div>
            
            <h4 class="text-lg font-semibold mb-2" style="color: var(--color-gray-900)">
              {index === 0 && "Mulai Proses"}
              {index === 1 && "Proses Berlangsung"}
              {index === 2 && "Finalisasi"}
            </h4>
            <p style="color: var(--color-gray-600)">
              {index === 0 && (step.subtext || "Langkah awal yang mudah dan cepat untuk memulai proses.")}
              {index === 1 && (step.subtext || "Proses berjalan dengan lancar dan efisien dengan tim ahli kami.")}
              {index === 2 && (step.subtext || "Hasil yang dijamin memuaskan dengan pengecekan menyeluruh.")}
            </p>
          </div>
        </div>
      ))}
    </div>
    
    {cta && (
      <div class="text-center mt-10">
        <Link 
          href={cta.url} 
          style="primary"
          size="lg"
          class="inline-flex items-center"
          target="_blank" 
          rel="noopener noreferrer nofollow"
        >
          {cta.text}
        </Link>
      </div>
    )}
  </div>
</section>

<style>
  @keyframes pulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(var(--color-brand-rgb), 0.4);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(var(--color-brand-rgb), 0);
    }
  }
  
  .process-marker {
    animation: pulse 2s infinite;
  }
  
  @media (hover: hover) {
    .process-marker {
      animation: none;
    }
    
    div:hover .process-marker {
      animation: pulse 2s infinite;
    }
  }
</style> 