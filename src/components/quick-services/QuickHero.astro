---
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

const { 
  title, 
  subtitle, 
  icon, 
  timeframe, 
  primaryButtonText, 
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink
} = Astro.props;
---

<main class="relative overflow-hidden hero-padding">  
  <!-- Background design elements -->
  <BackgroundElements />
  
  <div class="relative z-10 w-full max-w-[60ch] mx-auto px-4">
    <div class="flex flex-col items-center text-center">
      
      <h1 class="max-w-5xl mx-auto mb-3">
        <span class="relative inline-block">
          <span class="relative z-10">{title}</span>
        </span> 
      </h1>
      
      <p class="text-lg mt-3 max-w-3xl mx-auto" style="color: var(--color-gray-700)">
        {subtitle}
      </p>

      {timeframe && (
        <div class="mt-4 flex items-center justify-center px-4 py-2 bg-[var(--color-brand-50)] rounded-full">
          <Icon name="lucide:clock" class="w-4 h-4 mr-2 text-[var(--color-brand)]" />
          <span class="text-sm font-medium text-[var(--color-brand)]">{timeframe}</span>
        </div>
      )}
      
      <div class="mt-10 flex flex-col sm:flex-row gap-4 justify-center w-full">
        {secondaryButtonText && secondaryButtonLink && (
          <a href={secondaryButtonLink} rel="noopener noreferrer nofollow">
            <Button size="lg" style="neutral" class="flex gap-1 items-center justify-center w-full px-8 py-3">
              {secondaryButtonText}
            </Button>
          </a>
        )}
        {primaryButtonText && primaryButtonLink && (
          <a href={primaryButtonLink} rel="noopener noreferrer nofollow">
            <Button size="lg" class="flex gap-1 items-center justify-center w-full px-8 py-3">
              {primaryButtonText}
            </Button>
          </a>
        )}
      </div>
    </div>
  </div>
</main> 