---
import { Icon } from "astro-icon/components";
---

<div class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
  <div class="mx-auto max-w-7xl px-3 py-3 sm:px-6 lg:px-8">
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex w-0 flex-1 items-center">
        <span class="flex rounded-lg bg-blue-800 p-2">
          <Icon name="lucide:clock" class="h-5 w-5 text-white" />
        </span>
        <p class="ml-3 truncate font-medium text-white">
          <span class="md:hidden">
            Hanya tersisa 2 slot proyek untuk peluncuran Juni!
          </span>
          <span class="hidden md:inline">
            ⚡ Hanya tersisa 2 slot proyek untuk peluncuran Juni! Jangan sampai terlewat - konsultasi gratis sekarang.
          </span>
        </p>
      </div>
      <div class="order-3 mt-2 w-full flex-shrink-0 sm:order-2 sm:mt-0 sm:w-auto">
        <a
          href="/hubungi-kami"
          class="flex items-center justify-center rounded-md border border-transparent bg-white px-4 py-2 text-sm font-medium text-blue-600 shadow-sm hover:bg-blue-50 transition-colors"
        >
          Pesan Slot Sekarang
        </a>
      </div>
      <div class="order-2 flex-shrink-0 sm:order-3 sm:ml-3">
        <button
          type="button"
          id="dismiss-urgency-bar"
          class="-mr-1 flex rounded-md p-2 hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-white sm:-mr-2"
        >
          <span class="sr-only">Tutup</span>
          <Icon name="lucide:x" class="h-5 w-5 text-white" />
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const dismissButton = document.getElementById('dismiss-urgency-bar');
    const urgencyBar = dismissButton?.closest('div');
    
    dismissButton?.addEventListener('click', function() {
      if (urgencyBar) {
        urgencyBar.style.display = 'none';
        localStorage.setItem('urgencyBarDismissed', 'true');
      }
    });

    // Check if user already dismissed the bar
    if (localStorage.getItem('urgencyBarDismissed') === 'true') {
      if (urgencyBar) {
        urgencyBar.style.display = 'none';
      }
    }
  });
</script>