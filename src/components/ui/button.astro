---
interface Props {
  size?: "md" | "lg";
  block?: boolean;
  style?: "outline" | "primary" | "inverted" | "neutral" | "muted";
  class?: string;
  href?: string;
  [x: string]: any;
}

const {
  size = "md",
  style = "primary",
  block,
  class: className,
  href,
  ...rest
} = Astro.props;

const sizes = {
  md: "px-5 py-2.5",
  lg: "px-8 py-3",
};

const styles = {
  outline: "border-2 border-black hover:bg-black text-black hover:text-white",
  primary: "bg-[var(--color-brand)] text-[var(--color-brand-text)] hover:bg-[var(--color-brand-light)] border-2 border-transparent",
  neutral: "bg-neutral-200 text-neutral-700 hover:bg-neutral-800 hover:text-white border-2 border-transparent",
  inverted: "bg-white text-black   border-2 border-transparent",
  muted: "bg-gray-100 hover:bg-gray-200   border-2 border-transparent",
};
---

{href ? (
  <a
    href={href}
    {...rest}
    class:list={[
      "styled-button",
      `btn-style-${style}`,
      "rounded-sm text-center transition focus-visible:ring-2 ring-offset-2 ring-gray-200 hover:shadow-lg cursor-pointer font-medium",
      block && "w-full block",
      sizes[size],
      styles[style],
      className,
    ]}
  ><slot /></a>
) : (
  <button
    {...rest}
    class:list={[
      "styled-button",
      `btn-style-${style}`,
      "rounded-sm text-center transition focus-visible:ring-2 ring-offset-2 ring-gray-200 hover:shadow-lg cursor-pointer font-medium",
      block && "w-full block",
      sizes[size],
      styles[style],
      className,
    ]}
  ><slot /></button>
)}
