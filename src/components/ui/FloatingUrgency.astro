---
import { Icon } from "astro-icon/components";
---

<div 
  id="floating-urgency"
  class="fixed bottom-6 right-6 z-40 max-w-sm transform transition-all duration-300 ease-in-out"
  style="display: none;"
>
  <div class="rounded-lg bg-white shadow-lg ring-1 ring-gray-200 overflow-hidden">
    <!-- Header -->
    <div class="bg-gradient-to-r from-red-500 to-orange-500 px-4 py-2">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2 text-white">
          <Icon name="lucide:clock" class="h-4 w-4 animate-pulse" />
          <span class="text-sm font-semibold">Slot Terbatas!</span>
        </div>
        <button
          id="close-floating-urgency"
          class="text-white hover:text-red-100 transition-colors"
        >
          <Icon name="lucide:x" class="h-4 w-4" />
        </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-4">
      <p class="text-sm text-gray-900 font-medium mb-2">
        <PERSON>ya tersisa <span class="text-red-600 font-bold">2 slot proyek</span> untuk peluncuran Juni!
      </p>
      <p class="text-xs text-gray-600 mb-3">
        Jangan sampai terlewat kesempatan untuk launch website Anda bulan ini.
      </p>
      <a
        href="/hubungi-kami"
        class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center text-sm font-medium py-2 px-4 rounded-md transition-colors"
      >
        Pesan Slot Sekarang
      </a>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const floatingUrgency = document.getElementById('floating-urgency');
    const closeButton = document.getElementById('close-floating-urgency');
    
    // Show after 5 seconds if not dismissed
    setTimeout(() => {
      if (!localStorage.getItem('floatingUrgencyDismissed')) {
        if (floatingUrgency) {
          floatingUrgency.style.display = 'block';
          setTimeout(() => {
            floatingUrgency.style.transform = 'translateY(0)';
            floatingUrgency.style.opacity = '1';
          }, 100);
        }
      }
    }, 5000);
    
    // Close functionality
    closeButton?.addEventListener('click', function() {
      if (floatingUrgency) {
        floatingUrgency.style.transform = 'translateY(100px)';
        floatingUrgency.style.opacity = '0';
        setTimeout(() => {
          floatingUrgency.style.display = 'none';
        }, 300);
        localStorage.setItem('floatingUrgencyDismissed', 'true');
      }
    });
    
    // Auto-hide after 15 seconds
    setTimeout(() => {
      if (floatingUrgency && floatingUrgency.style.display !== 'none') {
        floatingUrgency.style.transform = 'translateY(100px)';
        floatingUrgency.style.opacity = '0';
        setTimeout(() => {
          floatingUrgency.style.display = 'none';
        }, 300);
      }
    }, 20000);
  });
</script>

<style>
  #floating-urgency {
    transform: translateY(100px);
    opacity: 0;
  }
  
  @media (max-width: 640px) {
    #floating-urgency {
      bottom: 1rem;
      right: 1rem;
      left: 1rem;
      max-width: none;
    }
  }
</style>