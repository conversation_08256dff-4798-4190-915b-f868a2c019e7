---
interface Props {
  href: string;
  size?: "md" | "lg" | "sm";
  block?: boolean;
  style?: "outline" | "primary" | "inverted" | "muted" | "neutral";
  class?: string;
  rel?: string;
  [x: string]: any;
}

const {
  href,
  block,
  size = "lg",
  style = "primary",
  class: className,
  rel,
  ...rest
} = Astro.props;

const sizes = {
  lg: "px-8 py-3",
  md: "px-4 py-2",
  sm: "px-4 py-1.5",
};

const styles = {
  outline: "bg-white border-2 border-black hover:bg-gray-100 text-black ",
  primary: "bg-[var(--color-brand)] text-[var(--color-brand-text)] hover:bg-[var(--color-brand-light)] border-2 border-transparent",
  inverted: "bg-white text-black   border-2 border-transparent",
  muted: "bg-gray-100 hover:bg-gray-200   border-2 border-transparent",
  neutral: "bg-neutral-200 text-neutral-700 hover:bg-neutral-400 border-2 border-transparent",
};
---

<a
  href={href}
  rel={rel}
  {...rest}
  class:list={[
    "rounded-sm text-center transition focus-visible:ring-2 ring-offset-2 ring-gray-200 hover:shadow-lg cursor-pointer font-medium",
    block && "w-full",
    sizes[size],
    styles[style],
    className,
  ]}
  ><slot />
</a>
