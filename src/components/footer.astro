---
// @ts-ignore
import { Icon } from "astro-icon/components";
import IconLogo from "@/assets/iconlogo.svg";
const currentYear = new Date().getFullYear();

const footerSections = [
  {
    title: "Layanan",
    links: [
      { name: "Pembuatan Website", href: "/jasa/pembuatan-website" },
      { name: "Redesign Website", href: "/jasa/redesign-website" },
      { name: "Maintenance Website", href: "/jasa/maintenance-website" },
      { name: "Konversi Website ke Blok", href: "/jasa/konversi-website-ke-blocks" },
      { name: "Perbaikan Website", href: "/jasa/perbaikan-website-wordpress" },
      { name: "Hapus Malware Website", href: "/jasa/hapus-malware-wordpress" },
      { name: "Migrasi Website", href: "/jasa/migrasi-website" },
      { name: "Optimasi SEO On-page", href: "/jasa/seo-onpage", badge: "New" },
      { name: "Migrasi WordPress ke Astro", href: "/jasa/migrasi-wordpress-ke-astro", badge: "New" },
      { name: "WordPress Hosting", href: "/jasa/wordpress-hosting", badge: "New" },
    ],
  },
  {
    title: "Tentang",
    links: [
      { name: "Tentang", href: "/tentang" },
      { name: "Meet Willya Randika", href: "/tentang#meet-willya-randika" },
      { name: "Teknologi", href: "/tentang#teknologi" },
      { name: "Komunikasi", href: "/tentang#komunikasi" },
      { name: "Komitmen", href: "/tentang#komitmen" },
    ],
  },
  {
    title: "Halaman",
    links: [
      { name: "Kontak", href: "/hubungi-kami" },
      { name: "Blog", href: "/blog" },
      { name: "Syarat dan ketentuan", href: "/syarat-dan-ketentuan-proyek" },
      { name: "Rekomendasi Plugin WordPress", href: "/plugin-wordpress-terbaik" },
    ],
  },
];
---

<footer class="relative overflow-hidden bg-gradient-to-b from-gray-900 to-gray-800 text-white">
  <!-- Decorative elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute -left-40 -bottom-40 w-96 h-96 rounded-full bg-[var(--color-brand)] opacity-5 blur-3xl"></div>
    <div class="absolute right-0 top-10 w-72 h-72 rounded-full bg-[var(--color-brand)] opacity-5 blur-3xl"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 py-20 relative z-10">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-12 mb-16">
      <!-- Kolom 1: Brand & Kontak -->
      <div class="md:col-span-4 space-y-6">
        <a href="/" class="flex items-center gap-2.5">
          <IconLogo class="w-6 h-6 text-[var(--color-brand)]" />
          <span class="font-bold text-xl tracking-tight text-white">HARUN STUDIO</span>
        </a>
        
        <p class="text-gray-300 mt-4 max-w-md">
          Solusi website WordPress yang handal, cepat, dan aman untuk bisnis yang serius tumbuh.
        </p>
        
        <div class="pt-6 space-y-4">
          <a href="https://wa.me/6281291274023" class="flex items-center gap-2.5 group transition-colors">
            <div class="bg-gray-800 rounded-full w-9 h-9 flex items-center justify-center group-hover:bg-[var(--color-brand)] transition-colors">
              <Icon name="lucide:message-square" class="w-4 h-4 text-gray-300 group-hover:text-white" />
            </div>
            <span class="text-gray-300 group-hover:text-white transition-colors">Chat dengan kami di WhatsApp</span>
          </a>
          
          <a href="tel:+6281291274023" class="flex items-center gap-2.5 group transition-colors">
            <div class="bg-gray-800 rounded-full w-9 h-9 flex items-center justify-center group-hover:bg-[var(--color-brand)] transition-colors">
              <Icon name="lucide:phone" class="w-4 h-4 text-gray-300 group-hover:text-white" />
            </div>
            <span class="text-gray-300 group-hover:text-white transition-colors">+62 812 9127 4023</span>
          </a>
        </div>
      </div>

      <!-- Kolom 2, 3, 4: Links -->
      {footerSections.map((section, index) => (
        <div class={`md:col-span-${index === 0 ? 3 : 2} space-y-5`}>
          <h2 class="text-sm font-semibold tracking-wider text-white uppercase">{section.title}</h2>
          <ul class="space-y-3">
            {section.links.map((link) => (
              <li>
                <a href={link.href} class="text-gray-400 hover:text-[var(--color-brand-50)] transition-colors inline-flex items-center gap-1.5">
                  {link.name}
                  {link.badge && (
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-700 ring-1 ring-red-200 animate-pulse">
                      {link.badge}
                    </span>
                  )}
                </a>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>

    <!-- Bottom Row -->
    <div class="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-400 mb-4 md:mb-0">
        © 2021 - {currentYear} Harun Studio. All rights reserved.
      </p>
      
      <span class="flex items-center gap-1.5 text-gray-400">
        Proudly built with
        <Icon name="lucide:heart" class="w-4 h-4" style="color: #e11d48" />
        and passion
      </span>
    </div>
  </div>
</footer>
