---
import <PERSON><PERSON> from "./ui/button.astro";
---

<!-- To make this contact form work, create your free access key from https://web3forms.com/
     Then you will get all form submissions in your email inbox. -->
<form
  action="https://api.web3forms.com/submit"
  method="POST"
  id="form"
  class="needs-validation"
  data-astro-reload
  novalidate>
  <input type="hidden" name="access_key" value="04e1a007-cdc5-4fe3-b5fa-2acb48dec894" />
  <!-- Create your free access key from https://web3forms.com/ -->
  <input type="checkbox" class="hidden" style="display:none" name="botcheck" />
  
  <h2 class="text-3xl font-bold mb-8 text-gray-900">Jelaskan tentang proyek Anda</h2>
  
  <div class="space-y-8">
    <!-- Service Selection -->
    <div class="form-group">
      <label for="service" class="block text-gray-700 font-medium mb-2"><PERSON><PERSON><PERSON> yang <PERSON><PERSON> butuhkan<span class="text-red-500">*</span></label>
      <div class="relative">
        <select
          id="service"
          name="service"
          required
          class="w-full px-4 py-3 border-2 placeholder:text-gray-500 rounded-md outline-none focus:ring-4 border-gray-300 focus:border-[var(--color-brand)] ring-[var(--color-brand-50)] appearance-none transition-all duration-300"
        >
          <option value="" disabled selected>Pilih layanan</option>
          <option value="Maintenance website">Maintenance website</option>
          <option value="Pembuatan website">Pembuatan website</option>
          <option value="Migrasi website">Migrasi website</option>
          <option value="Hapus malware WordPress">Hapus malware WordPress</option>
          <option value="Perbaikan website WordPress">Perbaikan website WordPress</option>
          <option value="Migrasi WordPress ke Astro">Migrasi WordPress ke Astro</option>
          <option value="Konversi ke blok WordPress">Konversi ke blok WordPress</option>
          <option value="Lainnya">Lainnya</option>
        </select>
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
        <div class="empty-feedback invalid-feedback text-red-500 text-sm mt-1">
          Silakan pilih layanan yang Anda butuhkan.
        </div>
      </div>
    </div>
  
    <!-- Two Column Layout -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Name Field -->
      <div class="form-group">
        <label for="name" class="block text-gray-700 font-medium mb-2">Nama lengkap<span class="text-red-500">*</span></label>
        <input
          type="text"
          id="name"
          placeholder="Masukkan nama lengkap Anda"
          required
          class="w-full px-4 py-3 border-2 placeholder:text-gray-500 rounded-md outline-none focus:ring-4 border-gray-300 focus:border-[var(--color-brand)] ring-[var(--color-brand-50)] transition-all duration-300"
          name="name"
        />
        <div class="empty-feedback invalid-feedback text-red-500 text-sm mt-1">
          Silakan masukkan nama lengkap Anda.
        </div>
      </div>
      
      <!-- Email Field -->
      <div class="form-group">
        <label for="email_address" class="block text-gray-700 font-medium mb-2">Alamat email<span class="text-red-500">*</span></label>
        <input
          id="email_address"
          type="email"
          placeholder="Masukkan alamat email Anda"
          name="email"
          required
          class="w-full px-4 py-3 border-2 placeholder:text-gray-500 rounded-md outline-none focus:ring-4 border-gray-300 focus:border-[var(--color-brand)] ring-[var(--color-brand-50)] transition-all duration-300"
        />
        <div class="empty-feedback text-red-500 text-sm mt-1">
          Silakan masukkan alamat email Anda.
        </div>
        <div class="invalid-feedback text-red-500 text-sm mt-1">
          Silakan masukkan alamat email yang valid.
        </div>
      </div>
    </div>
    
    <!-- Company Field -->
    <div class="form-group">
      <label for="company" class="block text-gray-700 font-medium mb-2">Nama Perusahaan <span class="text-gray-500 font-normal">(opsional)</span></label>
      <input
        type="text"
        id="company"
        placeholder="Masukkan nama perusahaan/bisnis Anda"
        class="w-full px-4 py-3 border-2 placeholder:text-gray-500 rounded-md outline-none focus:ring-4 border-gray-300 focus:border-[var(--color-brand)] ring-[var(--color-brand-50)] transition-all duration-300"
        name="company"
      />
    </div>
    
    <!-- Message Field -->
    <div class="form-group">
      <label for="message" class="block text-gray-700 font-medium mb-2">Mengapa Anda memutuskan untuk menghubungi saya secara spesifik<span class="text-red-500">*</span></label>
      <textarea
        id="message"
        name="message"
        required
        placeholder="Anda juga bisa bercerita tentang permasalahan atau kebutuhan digital bisnis Anda disini..."
        class="w-full px-4 py-3 border-2 placeholder:text-gray-500 rounded-md outline-none h-40 focus:ring-4 border-gray-300 focus:border-[var(--color-brand)] ring-[var(--color-brand-50)] transition-all duration-300"
      ></textarea>
      <div class="empty-feedback invalid-feedback text-red-500 text-sm mt-1">
        Silakan jelaskan kebutuhan Anda.
      </div>
    </div>
    
    <!-- Privacy Notes -->
    <div class="text-sm text-gray-500 mb-4">
      <p>Dengan mengirimkan formulir ini, Anda menyetujui kebijakan privasi kami. Data Anda hanya digunakan untuk merespons permintaan Anda.</p>
    </div>
    
    <!-- Submit Button -->
    <div>
      <button 
        type="submit" 
        class="w-full py-3 px-6 rounded-md bg-[var(--color-brand)] hover:bg-[var(--color-brand-hover)] text-white font-medium text-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-[var(--color-brand-50)] cursor-pointer"
      >
        Kirim Pesan
      </button>
      <div id="form-status" class="mt-4">
        <div id="result" class="text-center"></div>
        <div id="sending" class="hidden text-center">
          <div class="inline-flex items-center py-1 px-4 rounded-full bg-blue-50 text-blue-500">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Mengirim...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<style>
  .invalid-feedback,
  .empty-feedback {
    display: none;
  }

  .was-validated :placeholder-shown:invalid ~ .empty-feedback {
    display: block;
  }

  .was-validated :not(:placeholder-shown):invalid ~ .invalid-feedback {
    display: block;
  }

  .is-invalid,
  .was-validated :invalid {
    border-color: #dc3545 !important;
  }
  
  .form-group:hover label {
    color: var(--color-brand);
  }
  
  input:focus, select:focus, textarea:focus {
    border-color: var(--color-brand) !important;
  }
  
  input:hover, select:hover, textarea:hover {
    border-color: #d1d5db;
  }
</style>

<script is:inline>
  document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById("form");
    const result = document.getElementById("result");
    const sending = document.getElementById("sending");

    form.addEventListener("submit", function (e) {
      e.preventDefault();
      form.classList.add("was-validated");
      if (!form.checkValidity()) {
        form.querySelectorAll(":invalid")[0].focus();
        return;
      }
      
      // Show sending indicator
      result.innerHTML = "";
      sending.classList.remove("hidden");
      
      const formData = new FormData(form);
      const object = Object.fromEntries(formData);
      const json = JSON.stringify(object);

      fetch("https://api.web3forms.com/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: json,
      })
        .then(async (response) => {
          let json = await response.json();
          // Hide sending indicator
          sending.classList.add("hidden");
          
          if (response.status == 200) {
            result.innerHTML = `
              <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative" role="alert">
                <p class="font-medium">Pesan berhasil terkirim!</p>
                <p class="text-sm">Kami akan segera menghubungi Anda melalui email yang telah Anda berikan.</p>
              </div>
            `;
          } else {
            console.log(response);
            result.innerHTML = `
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                <p class="font-medium">Terjadi kesalahan!</p>
                <p class="text-sm">${json.message || "Silakan coba lagi dalam beberapa saat."}</p>
              </div>
            `;
          }
        })
        .catch((error) => {
          // Hide sending indicator
          sending.classList.add("hidden");
          
          console.log(error);
          result.innerHTML = `
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <p class="font-medium">Terjadi kesalahan!</p>
              <p class="text-sm">Silakan coba lagi dalam beberapa saat.</p>
            </div>
          `;
        })
        .then(function () {
          form.reset();
          form.classList.remove("was-validated");
          setTimeout(() => {
            result.innerHTML = "";
          }, 5000);
        });
    });
  });
</script>
